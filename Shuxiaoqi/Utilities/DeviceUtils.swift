import Foundation
import UIKit
import Darwin

/// 设备相关工具
struct DeviceUtils {
    /// 返回用户在系统中设置的自定义设备名称；若获取失败，则回退到 UIDevice.current.name
    static func deviceCustomName() -> String {
        // 1. 先尝试 host name
        var buffer = [CChar](repeating: 0, count: 256)
        if gethostname(&buffer, buffer.count) == 0 {
            let hostName = String(cString: buffer)
            if !hostName.isEmpty && !isGenericHostName(hostName) {
                return hostName
            }
        }
        // 2. 回退 UIDevice.current.name 并做清洗
        return sanitize(UIDevice.current.name)
    }
    
    /// 判断是否为通用占位 host name（如 localhost / iPhone 等）
    private static func isGenericHostName(_ name: String) -> Bool {
        let lower = name.lowercased()
        let generic: Set<String> = ["localhost", "iphone", "ipad", "ipod", "apple-tv"]
        return generic.contains(lower)
    }
    
    /// 去除空格、引号及非 ASCII 字符，避免服务器解析异常
    private static func sanitize(_ name: String) -> String {
        // Remove spaces and quotes
        var cleaned = name.replacingOccurrences(of: " ", with: "")
                         .replacingOccurrences(of: "'", with: "")
                         .replacingOccurrences(of: "\"", with: "")
        // Keep only ASCII to避免emoji
        cleaned = cleaned.filter { $0.isASCII }
        return cleaned
    }
    
    /// 返回系统硬件标识符，例如 "iPhone12,1"
    static func hardwareIdentifier() -> String {
        var systemInfo = utsname()
        uname(&systemInfo)
        let identifier = withUnsafePointer(to: &systemInfo.machine) { ptr in
            return String(cString: UnsafeRawPointer(ptr).assumingMemoryBound(to: CChar.self))
        }
        return identifier
    }
    
    /// 将硬件标识符映射为市场名称（如 "iPhone11" → "iPhone 8"，"iPhone12,1" → "iPhone 11"）
    static func marketingDeviceModel() -> String {
        let identifier = hardwareIdentifier()
        // 参考 https://www.theiphonewiki.com/wiki/Models 最新列表（截取常用）
        switch identifier {
        // iPhone 11 系列
        case "iPhone12,1": return "iPhone 11"
        case "iPhone12,3": return "iPhone 11 Pro"
        case "iPhone12,5": return "iPhone 11 Pro Max"
        // iPhone 12 系列
        case "iPhone13,1": return "iPhone 12 mini"
        case "iPhone13,2": return "iPhone 12"
        case "iPhone13,3": return "iPhone 12 Pro"
        case "iPhone13,4": return "iPhone 12 Pro Max"
        // iPhone 13 系列
        case "iPhone14,4": return "iPhone 13 mini"
        case "iPhone14,5": return "iPhone 13"
        case "iPhone14,2": return "iPhone 13 Pro"
        case "iPhone14,3": return "iPhone 13 Pro Max"
        // iPhone 14 系列
        case "iPhone14,7": return "iPhone 14"
        case "iPhone14,8": return "iPhone 14 Plus"
        case "iPhone15,2": return "iPhone 14 Pro"
        case "iPhone15,3": return "iPhone 14 Pro Max"
        // iPhone 15 系列（示例）
        case "iPhone15,4": return "iPhone 15"
        case "iPhone15,5": return "iPhone 15 Pro"
        // iPhone X / 8 / XR / XS 系列
        case "iPhone10,1", "iPhone10,4": return "iPhone 8"
        case "iPhone10,2", "iPhone10,5": return "iPhone 8 Plus"
        case "iPhone10,3", "iPhone10,6": return "iPhone X"
        case "iPhone11,8": return "iPhone XR"
        case "iPhone11,2": return "iPhone XS"
        case "iPhone11,4", "iPhone11,6": return "iPhone XS Max"
        // iPad（选取常见）
        case "iPad13,1", "iPad13,2": return "iPad Air (4th generation)"
        case "iPad14,3", "iPad14,4": return "iPad Pro 11-inch (4th generation)"
        default:
            return identifier // 未匹配则回传原始标识符
        }
    }

    // MARK: - 设备信息管理

    /// 当前设备信息结构
    struct CurrentDeviceInfo: Codable {
        let deviceId: String
        let deviceName: String
        let deviceSystem: String
        let deviceType: Int
        let ip: String
        let location: String
        let timestamp: TimeInterval

        init() {
            self.deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
            self.deviceName = DeviceUtils.marketingDeviceModel()
            self.deviceSystem = UIDevice.current.systemVersion
            self.deviceType = 1 // iOS设备
            self.ip = DeviceUtils.getCurrentIP()
            self.location = ""
            self.timestamp = Date().timeIntervalSince1970
        }

        init(deviceId: String, deviceName: String, deviceSystem: String, deviceType: Int, ip: String, location: String) {
            self.deviceId = deviceId
            self.deviceName = deviceName
            self.deviceSystem = deviceSystem
            self.deviceType = deviceType
            self.ip = ip
            self.location = location
            self.timestamp = Date().timeIntervalSince1970
        }
    }

    /// 获取当前设备IP地址（优先WiFi，其次蜂窝网络）
    static func getCurrentIP() -> String {
        return NetworkUtils.getLocalIPAddress() ?? ""
    }

    /// 保存当前设备信息到本地
    static func saveCurrentDeviceInfo(_ deviceInfo: CurrentDeviceInfo) {
        if let data = try? JSONEncoder().encode(deviceInfo) {
            UserDefaults.standard.set(data, forKey: "currentDeviceInfo")
            UserDefaults.standard.synchronize()
        }
    }

    /// 从本地获取当前设备信息
    static func getCurrentDeviceInfo() -> CurrentDeviceInfo? {
        guard let data = UserDefaults.standard.data(forKey: "currentDeviceInfo"),
              let deviceInfo = try? JSONDecoder().decode(CurrentDeviceInfo.self, from: data) else {
            return nil
        }
        return deviceInfo
    }

    /// 检查是否为当前设备
    static func isCurrentDevice(deviceId: String?, deviceName: String?, ip: String?) -> Bool {
        let currentDeviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
        let currentDeviceName = DeviceUtils.marketingDeviceModel()
        let currentIP = DeviceUtils.getCurrentIP()

        // 优先通过设备ID判断
        if let deviceId = deviceId, !deviceId.isEmpty {
            return deviceId == currentDeviceId
        }

        // 其次通过设备名称和IP组合判断
        if let deviceName = deviceName, let ip = ip {
            return deviceName == currentDeviceName && ip == currentIP
        }

        // 最后通过设备名称判断
        if let deviceName = deviceName {
            return deviceName == currentDeviceName
        }

        return false
    }

    /// 获取公网IP地址（异步）
    static func getPublicIP(completion: @escaping (String?) -> Void) {
        NetworkUtils.getPublicIPAddress(completion: completion)
    }
}
