//
//  NetworkUtils.swift
//  Shuxiaoqi
//
//  Created by AI Assistant on 2025/7/29.
//

import Foundation
import Network
import SystemConfiguration

/// 网络相关工具类
struct NetworkUtils {
    
    /// 获取当前设备的本地IP地址
    static func getLocalIPAddress() -> String? {
        var address: String?
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        
        guard getifaddrs(&ifaddr) == 0 else {
            return nil
        }
        
        var ptr = ifaddr
        while ptr != nil {
            defer { ptr = ptr?.pointee.ifa_next }
            
            guard let interface = ptr?.pointee else { continue }
            
            let addrFamily = interface.ifa_addr.pointee.sa_family
            if addrFamily == UInt8(AF_INET) {
                let name = String(cString: interface.ifa_name)
                
                // 优先获取WiFi地址(en0)，其次是蜂窝网络(pdp_ip0)
                if name == "en0" || name == "pdp_ip0" {
                    var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                    getnameinfo(interface.ifa_addr, socklen_t(interface.ifa_addr.pointee.sa_len),
                               &hostname, socklen_t(hostname.count),
                               nil, socklen_t(0), NI_NUMERICHOST)
                    let ipAddress = String(cString: hostname)
                    
                    // 优先返回WiFi地址
                    if name == "en0" {
                        address = ipAddress
                        break
                    } else if address == nil {
                        address = ipAddress
                    }
                }
            }
        }
        
        freeifaddrs(ifaddr)
        return address
    }
    
    /// 获取公网IP地址（异步）
    static func getPublicIPAddress(completion: @escaping (String?) -> Void) {
        let ipServices = [
            "https://api.ipify.org?format=text",
            "https://ipinfo.io/ip",
            "https://icanhazip.com",
            "https://checkip.amazonaws.com"
        ]
        
        func tryService(at index: Int) {
            guard index < ipServices.count else {
                completion(nil)
                return
            }
            
            guard let url = URL(string: ipServices[index]) else {
                tryService(at: index + 1)
                return
            }
            
            var request = URLRequest(url: url)
            request.timeoutInterval = 5.0
            request.cachePolicy = .reloadIgnoringLocalCacheData
            
            URLSession.shared.dataTask(with: request) { data, response, error in
                if let data = data,
                   let ipString = String(data: data, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines),
                   !ipString.isEmpty,
                   isValidIPAddress(ipString) {
                    DispatchQueue.main.async {
                        completion(ipString)
                    }
                } else {
                    tryService(at: index + 1)
                }
            }.resume()
        }
        
        tryService(at: 0)
    }
    
    /// 验证IP地址格式是否正确
    private static func isValidIPAddress(_ ip: String) -> Bool {
        let parts = ip.components(separatedBy: ".")
        guard parts.count == 4 else { return false }
        
        for part in parts {
            guard let num = Int(part), num >= 0 && num <= 255 else {
                return false
            }
        }
        return true
    }
    
    /// 获取网络类型
    static func getNetworkType() -> String {
        let reachability = SCNetworkReachabilityCreateWithName(nil, "www.apple.com")
        var flags = SCNetworkReachabilityFlags()
        
        guard let reachabilityRef = reachability,
              SCNetworkReachabilityGetFlags(reachabilityRef, &flags) else {
            return "Unknown"
        }
        
        if flags.contains(.reachable) {
            if flags.contains(.isWWAN) {
                return "Cellular"
            } else {
                return "WiFi"
            }
        }
        
        return "No Connection"
    }
    
    /// 检查网络连接状态
    static func isNetworkAvailable() -> Bool {
        let reachability = SCNetworkReachabilityCreateWithName(nil, "www.apple.com")
        var flags = SCNetworkReachabilityFlags()
        
        guard let reachabilityRef = reachability,
              SCNetworkReachabilityGetFlags(reachabilityRef, &flags) else {
            return false
        }
        
        return flags.contains(.reachable) && !flags.contains(.connectionRequired)
    }
}
