import Foundation
import UIKit
import Alamofire

/// API业务层
class APIManager {
    static let shared = APIManager()
    private init() {}
    
    // MARK: - 加密相关
    
    /// 加密密码
    /// - Parameter password: 明文密码
    /// - Returns: 加密后的密码字符串，失败返回nil
    func encryptPassword(_ password: String) -> String? {
        return RASManager.shared.encryptPassword(password)
    }
    
    /// 使用RSA加密字符串
    /// - Parameters:
    ///   - plainText: 明文字符串
    ///   - publicKey: 公钥（Base64编码），如不提供则使用默认公钥
    ///   - padding: 填充方式，默认为PKCS1
    /// - Returns: 加密后的Base64字符串，失败返回nil
    func rsaEncrypt(_ plainText: String, publicKey: String? = nil, padding: RSAPadding = .PKCS1) -> String? {
        return RASManager.shared.encrypt(plainText, publicKey: publicKey, padding: padding)
    }
    
    // MARK: - 用户相关接口
    /// 退出登录
    func logout(completion: @escaping (Result<LoginResponse, APIError>) -> Void) {
        let request = APIRouter.User.logout

        APIService.shared.request(request, completion: completion)
    }

    /// 注销账号
    func logoutUser(
        logoutCode: String,
        logoutRemark: String,
        logoutType: Int,
        completion: @escaping (Result<LoginResponse, APIError>) -> Void
    ) {
        let request = APIRouter.User.logoutUser(
            logoutCode: logoutCode,
            logoutRemark: logoutRemark,
            logoutType: logoutType
        )

        APIService.shared.request(request, completion: completion)
    }
    
    /// 获取图形验证码
    func getCaptcha(codeId: String, type: String, completion: @escaping (Result<UIImage?, APIError>) -> Void) {
        let request = APIRouter.User.getCaptcha(codeId: codeId, type: type)
        
        APIService.shared.requestImage(request) { result in
            switch result {
            case .success(let imageData):
                // 将 Data 转换为 UIImage
                if let image = UIImage(data: imageData) {
                    completion(.success(image))
                } else {
                    completion(.success(nil))
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    /// 获取短信验证码
    func getSMSCode(
        phone: String,
        captchaCode: String,
        codeId: String,
        type: String,
        completion: @escaping (Result<SMSCodeResponse, APIError>) -> Void
    ) {
        let request = APIRouter.User.getSMSCode(phone: phone, captchaCode: captchaCode, codeId: codeId, type: type)
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 获取通用验证码
    func getCommonMSG(codeId: String, captchaCode: String, completion: @escaping (Result<SMSCodeResponse, APIError>)-> Void
    ) {
        let request = APIRouter.User.getCommonMsg(captchaCode: captchaCode, codeId: codeId)
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 验证通用验证码
    func checkCommonMsg(
        code: String,
        completion: @escaping (Result<VerificationResponse, APIError>) -> Void
    ) {
        let request = APIRouter.User.checkCommonMsg(code: code)
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 用户登录
    func login(
        phone: String,
        code: String,
        inviteCode: Int = 0,
        completion: @escaping (Result<LoginResponse, APIError>) -> Void
    ) {
        let request = APIRouter.User.login(phone: phone, code: code)
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 用户一键登录 (兼容旧接口名)
    @available(*, deprecated, renamed: "loginWithOneClickToken(token:completion:)")
    func oneKeyLogin(
        accessToken: String,
        completion: @escaping (Result<LoginResponse, APIError>) -> Void
    ) {
        // 为向后兼容，直接调用新版方法
        loginWithOneClickToken(token: accessToken, completion: completion)
    }
    
    /// 更换新的手机号
    func bangPhone(
        phone: String,
        code: String,
        checkId: String,
        completion: @escaping (Result<LoginResponse, APIError>) -> Void
    ) {
        let request = APIRouter.User.bangPhone(phone: phone, code: code, checkId: checkId)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //微信登录绑定手机号
    func bingUserPhone(
        bingPhoneId: String,
        code: String,
        phone: String,
        completion: @escaping (Result<LoginResponse, APIError>) -> Void
    ) {
        let request = APIRouter.User.bingUserPhone(bingPhoneId: bingPhoneId, code: code, phone: phone)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //密码登录
    func loginWithPassword(
        phone: String,
        password: String,
        completion: @escaping (Result<LoginResponse, APIError>) -> Void
    ) {
        let request = APIRouter.User.loginWithPassword(phone: phone, password: password)
        
        APIService.shared.request(request, completion: completion)
    }
    
    // 新增：获取当前用户脱敏手机号
    func getUserPhone(completion: @escaping (Result<UserPhoneResponse, APIError>) -> Void) {
        let request = APIRouter.User.getUserPhone
        
        APIService.shared.request(request, completion: completion)
    }
    
    //忘记密码-验证
    func checkUserPwdMsg(
        phone: String,
        code: String,
        completion: @escaping (Result<VerificationResponse, APIError>) -> Void) {
            let request = APIRouter.User.checkUserPwdMsg(phone: phone, code: code)
            
            APIService.shared.request(request, completion: completion)
        }
    
    //忘记密码-设置
    func bingUserPwd(
        phone: String,
        password: String,
        checkId: String,
        completion: @escaping (Result<LoginResponse, APIError>) -> Void
    ) {
        let request = APIRouter.User.bingUserPwd(phone: phone, checkId: checkId, password: password)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //密码更换
    func changePassword(
        newPassword: String,
        checkId: String,
        completion: @escaping (Result<VerificationResponse, APIError>) -> Void
    ) {
        let request = APIRouter.User.changePassword(checkId: checkId, passWord: newPassword)
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 获取用户信息
    func getUserInfo(completion: @escaping (Result<UserInfoResponse, APIError>) -> Void) {
        let request = APIRouter.Center.getUserInfo
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 侧边栏获取用户信息
    func getUserMain(completion: @escaping (Result<UserInfoResponse, APIError>) -> Void) {
        let request = APIRouter.Main.getUserMain
        
        APIService.shared.request(request, completion: completion)
    }
    
    //获取用户收藏视频列表
    func getUserWorksCollect(page: Int, size: Int, completion: @escaping (Result<VideoCollectionListResponse, APIError>) -> Void) {
        let request = APIRouter.Center.getUserWorksCollect(page: page, size: size)
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 获取帐户设置信息
    func getAccountSetInfo(completion: @escaping (Result<AccountSetInfoResponse, APIError>) -> Void) {
        let request = APIRouter.User.getAccountSetInfo
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 获取通知设置
    func getUserNoticeSetting(completion: @escaping (Result<UserNoticeSettingResponse, APIError>) -> Void) {
        let request = APIRouter.User.getUserNoticeSet
        
        APIService.shared.request(request, completion: completion)
        
    }
    
    /// 修改通知设置（按钮或配置项）
    /// - Parameters:
    ///   - noticeButtonId: 顶部按钮id（可选）
    ///   - noticeConfigId: 下方配置项id（可选）
    ///   - state: 状态 0-关闭 1-开启
    ///   - completion: 回调
    func setUserNoticeSet(
        noticeButtonId: Int?,
        noticeConfigId: Int?,
        state: Int,//state    通知设置状态 0-关闭 1-开启
        completion: @escaping (Result<UserNoticeSettingResponse, APIError>) -> Void
    ) {
        var params: [String: Any] = ["state": state]
        if let buttonId = noticeButtonId {
            params["noticeButtonId"] = buttonId
        }
        if let configId = noticeConfigId {
            params["noticeConfigId"] = configId
        }
        // 至少有一个id
        guard params["noticeButtonId"] != nil || params["noticeConfigId"] != nil else {
            print("setUserNoticeSet: 必须传递noticeButtonId或noticeConfigId中的一个")
            return
        }
        let request = APIRouter.User.setUserNoticeSetV2(params: params)
        APIService.shared.request(request, completion: completion)
    }
    
    // MARK: - 视频上传相关接口
    
    /// 获取腾讯云VOD上传签名
    func getVodSignature(completion: @escaping (Result<VodSignatureResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getVodSignature
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 上传短视频作品信息
    func uploadShortVideoWorks(
        params: [String : Any],
        completion: @escaping (Result<ShortVideoWorksAddResponse, APIError>) -> Void
    ) {
        let request = APIRouter.Video.shortVideoWorksAdd(params: params)
        APIService.shared.request(request, completion: completion)
    }
    
    // MARK: - 个人相关接口
    //获取黑名单列表
    func getBlackList(page: Int, size: Int, completion: @escaping (Result<BlackListResponse, APIError>) -> Void) {
        let request = APIRouter.Center.getBlackList(page: page, size: size)
        
        APIService.shared.request(request, completion: completion)
    }
    
    func getSvUserBlackCount(page: Int, size: Int, completion: @escaping (Result<BlackListNumResponse, APIError>) -> Void) {
        let request = APIRouter.Center.getSvUserBlackCount()
        
        APIService.shared.request(request, completion: completion)
    }
    
    //移除黑名单
    func clearUserBlock(customerId: String, completion: @escaping (Result<BlackListResponse, APIError>) -> Void) {
        let request = APIRouter.Center.clearUserBlock(customerId: customerId)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //拉黑用户
    func doUserBlock(customerId: String, completion: @escaping (Result<BlackListResponse, APIError>) -> Void) {
        let request = APIRouter.Center.doUserBlock(customerId: customerId)

        APIService.shared.request(request, completion: completion)
    }

    //获取粉丝列表
    func getFansList(page: Int, size: Int, completion: @escaping (Result<FansListResponse, APIError>) -> Void) {
        let request = APIRouter.Center.getFollowMyUser(page: page, size: size)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //关注用户
    func followUser‌(customerId: String, type: Int, worksId: Int, completion: @escaping (Result<FansListResponse, APIError>) -> Void) {
        let request = APIRouter.Center.followUser‌(customerId: customerId, type: type)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //获取关注列表
    func getFollowList(page: Int, size: Int, name: String, completion: @escaping (Result<FansListResponse, APIError>) -> Void) {
        let request = APIRouter.Center.getMyAttention(page: page, size: size, name: name)
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 推荐关注列表
    func getAttentionList(completion: @escaping (Result<RecommendedUsersListResponse, APIError>) -> Void) {
        let request = APIRouter.Center.getAttention
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 获取用户作品列表(创作中心，内容管理等需要
    func getPersonalWorksList(page: Int, size: Int, duration: Int? = nil, keywords: String? = nil, sort: Int? = nil, state: Int? = nil, time: Int? = nil, worksCategoryId: String? = nil, completion: @escaping (Result<PersonalWorksListResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getPersonalWorksList(page: page, size: size, duration: duration, keywords: keywords, sort: sort, state: state, time: time, worksCategoryId: worksCategoryId)
        
        APIService.shared.request(request, completion: completion)
    }
    
    /// 内容操作（上架、下架、删除）
    /// - Parameters:
    ///   - type: 操作类型 1-下架 2-上架 3-删除
    ///   - worksIds: 作品id集合
    ///   - completion: 回调
    func shortVideoWorksOperate(type: Int, worksIds: [String], completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Video.shortVideoWorksOperate(type: type, worksIds: worksIds)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //获取滤镜列表
    func getFilterList(type: String, completion: @escaping (Result<VideoEditConfigResponse, APIError>) -> Void) {
        let request = APIRouter.Effects.getVideoEditConfig(type: type)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //获取音乐列表
    func getVideoEditMusicList(collect: Bool, page: Int, size: Int, completion: @escaping (Result<VideoEditMusicListResponse, APIError>) -> Void) {
        let request = APIRouter.Effects.getVideoEditMusicList(collect: collect, page: page, size: size)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //操作音乐  操作 1-收藏 2-取消
    func videoEditCollectingMusic(id: Int, operate: Int, completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        
        let request = APIRouter.Effects.videoEditCollectingMusic(id: id, operate: operate)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //获取草稿箱列表
    func getVideoWorksDraftsList(page:Int ,size:Int, keywords: String? = nil, completion: @escaping (Result<ShortVideoListResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getVideoWorksDraftsList(params: ["page": page, "size": size, "keywords": keywords ?? ""])
        
        APIService.shared.request(request, completion: completion)
    }
    
    //删除草稿箱中的视频，传入ids即可
    func deleteVideoWorksDrafts(ids: [Int], completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Video.shortVideoWorksDraftsDelete(ids: ids)
        
        APIService.shared.request(request, completion: completion)
    }

    //存入草稿箱
    func saveVideoWorksDrafts(params: [String: Any], completion: @escaping (Result<ShortVideoListResponse, APIError>) -> Void) {
        let request = APIRouter.Video.shortVideoWorksDraftsAdd(params: params)
        
        APIService.shared.request(request, completion: completion)
    }
    
    
    //获取分类列表
    func getVideoTypeList(isHomeShow: Bool , completion: @escaping (Result<MainTypeListResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getVideoTypeList(isHomeShow: isHomeShow)
        
        APIService.shared.request(request, completion: completion)
    }

    // 获取首页 tab 分类列表
    // 上方bar传0，分类传1
    func getAppHomeConfigList(type: Int, completion: @escaping (Result<AppHomeConfigResponse, APIError>) -> Void) {
        let request = APIRouter.Main.getAppHomeConfigList(type: type)

        APIService.shared.request(request, completion: completion)
    }
    
    //获取用户点赞列表
    func getVideoLikesList(page: Int, size: Int,completion: @escaping (Result<VideoCollectionListResponse, APIError>) -> Void) {
        let request = APIRouter.Center.getUserWorksLike(page: page, size: size)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //操作视频点赞和收藏
    func doWorksLikeAndCollect(operateValue: Int, operateType: Int, worksIds: [String] ,completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Center.doWorksLikeAndCollect(operateValue: operateValue, operateType: operateType, worksIds: worksIds)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //个人主页-信息
    func getPersonHomeInfo( customerId: String, completion: @escaping (Result<PersonalHomeInfoResponse, APIError>) -> Void) {
        let request = APIRouter.PersonHome.getPersonHomeInfo(customerId: customerId)
        
        APIService.shared.request(request, completion: completion)
    }
    //个人主页-作品
    func getPersonHomeWorks( customerId: String, page: Int, size: Int, completion: @escaping (Result<VideoCollectionListResponse, APIError>) -> Void) {
        let request = APIRouter.PersonHome.getPersonHomeWorks(customerId: customerId, page: page, size: size)
        APIService.shared.request(request, completion: completion)
    }
    //个人主页-收藏
    func doWorksLikeAndCollect( customerId: String, page:Int, size: Int, completion: @escaping (Result<VideoCollectionListResponse, APIError>) -> Void) {
        let request = APIRouter.PersonHome.getPersonHomeWorksCollect(customerId: customerId, page: page, size: size)
        APIService.shared.request(request, completion: completion)
    }
    //个人主页-喜欢
    func getPersonHomeWorksLike( customerId: String, page: Int, size: Int, completion: @escaping (Result<VideoCollectionListResponse, APIError>) -> Void) {
        let request = APIRouter.PersonHome.getPersonHomeWorksLike(customerId: customerId, page: page, size: size)
        APIService.shared.request(request, completion: completion)
    }
    
    // 微信登录
    func loginWithWeChat(code: String, completion: @escaping (Result<LoginResponse, APIError>) -> Void) {
        let request = APIRouter.User.wechatLogin(code: code)
        APIService.shared.request(request, completion: completion)
    }

    // 微信绑定
    func bindWechat(code: String, isOperateBind: Bool, completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.User.bindWechat(code: code, isOperateBind: isOperateBind)
        APIService.shared.request(request, completion: completion)
    }
    
    // 一键登录
    func loginWithOneClickToken(token: String, completion: @escaping (Result<LoginResponse, APIError>) -> Void) {
        // 调用一键登录API
        let request = APIRouter.User.loginOneTouch(accessToken: token)
        APIService.shared.request(request, completion: completion)
    }
    
    // 七牛云文件上传（multipart/form-data，表单字段名为 file）
    func uploadFileQNRaw(files: [Data], completion: @escaping (Result<qiNiuContentResponse, APIError>) -> Void) {
        let request = APIRouter.Video.uploadFileQN
        // 通过 multipart/form-data 上传文件
        APIService.shared.uploadFiles(request, files: files, completion: completion)
    }
    
    //获取用户资料-编辑页
    func getCustomerInfo(completion: @escaping (Result<CustomerInfoResponse, APIError>) -> Void) {
        let request = APIRouter.Main.getCustomerInfo
        
        APIService.shared.request(request, completion: completion)
    }
    
    //保存用户资料-编辑页
    func setUserInfo(params: [String : Any], completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Main.setUserInfo(params: params)
        
        APIService.shared.request(request, completion: completion)
    }
    
    //保存用户头像
    func setUserAvatar(AvatarUrl: String, completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Main.setUserAvatar(AvatarUrl: AvatarUrl)
        APIService.shared.request(request, completion: completion)
    }
    
    //获取用户互动管理配置
    func getUserInteractionSet(completion: @escaping (Result<UserInteractionSetResponse, APIError>) -> Void) {
        let request = APIRouter.User.getUserInteractionSet
        
        APIService.shared.request(request, completion: completion)
    }
    
    //设置用户互动管理配置
    func setUserInteractionSet(interactionConfigId: Int, interactiveSettingButton: Int, completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.User.setUserInteractionSet(interactionConfigId: interactionConfigId, interactiveSettingButton: interactiveSettingButton)
        //互动设置按钮(0-全部 1-我关注的 2-互相关注 3-不接收)
        APIService.shared.request(request, completion: completion)
    }
    
    //获取用户兴趣标签
    func getUserLabel(completion: @escaping (Result<UserInterestLabelResponse, APIError>) -> Void) {
        let request = APIRouter.User.getUserLabel
        APIService.shared.request(request, completion: completion)
    }
    
    //保存用户兴趣标签
    func setUserLabels(ids:[Int] ,completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.User.setUserLabels(ids: ids)
        APIService.shared.request(request, completion: completion)
    }
    
    /// 搜索学校
    /// - Parameters:
    ///   - name: 关键字
    ///   - completion: 回调，返回 SchoolSearchResponse
    func searchSchool(name: String, completion: @escaping (Result<SchoolSearchResponse, APIError>) -> Void) {
        let request = APIRouter.Search.searchSchool(name: name)
        APIService.shared.request(request, completion: completion)
    }

    //获取地区选项
    func getRegionOption(params: [String: Any], completion: @escaping (Result<RegionOptionResponse, APIError>) -> Void) {
        let request = APIRouter.Main.getRegionOption(params: params)
        APIService.shared.request(request, completion: completion)
    }

    //获取更新弹窗信息
    func getAppVersion(appVersion: String, completion: @escaping (Result<AppVersionResponse, APIError>) -> Void) {
        let request = APIRouter.Main.getAppVersion(appVersion: appVersion)
        APIService.shared.request(request, completion: completion)
    }
    
    //初始化
    func firstEnter(deviceId: String, deviceName:String, deviceSystem:String, deviceType:Int, loginAddress:String, completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.User.firstEnter(deviceId: deviceId, deviceName: deviceName, deviceSystem: deviceSystem, deviceType: deviceType, loginAddress: loginAddress)
        APIService.shared.request(request, completion: completion)
    }

    //获取登录设备
    func getLoginDevice(completion: @escaping (Result<LoginDeviceListResponse, APIError>) -> Void) {
        let request = APIRouter.User.getLoginDevice()
        APIService.shared.request(request, completion: completion)
    }

    //搜索相关
    //搜索团购+外卖
    // func searchShop(params: [String: Any], completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
    //     let request = APIRouter.Search.searchShop(params: params)
    //     APIService.shared.request(request, completion: completion)
    // }

    // //商品搜索
    // func searchGood(params: [String: Any], completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
    //     let request = APIRouter.Search.searchGood(params: params)
    //     APIService.shared.request(request, completion: completion)
    // }

    //搜索视频
    func searchVideo(keywords: String, page: Int, size: Int, sort: Int, completion: @escaping (Result<VideoCollectionListResponse, APIError>) -> Void) {
        let request = APIRouter.Search.searchWorksMain(keywords: keywords, page: page, size: size, sort: sort)
        APIService.shared.request(request, completion: completion)
    }

    //搜索用户
    func searchUser(keywords: String, page: Int, size: Int, completion: @escaping (Result<UserSearchResultsResponse, APIError>) -> Void) {
        let request = APIRouter.Search.searchUser(keywords: keywords, page: page, size: size)
        APIService.shared.request(request, completion: completion)
    }

    //搜索历史词
    func listSearchHistory(params: [String: Any], completion: @escaping (Result<SearchkeywordsResponse, APIError>) -> Void) {
        let request = APIRouter.Search.listSearchHistory(params: params)
        APIService.shared.request(request, completion: completion)
    }

    //搜索热词
    func listSearchRecommend(params: [String: Any], completion: @escaping (Result<SearchkeywordsResponse, APIError>) -> Void) {
        let request = APIRouter.Search.listSearchRecommend(params: params)
        APIService.shared.request(request, completion: completion)
    }

    //获取用户历史评论
    func getCommentList(page: Int, size: Int, completion: @escaping (Result<CommentListResponse, APIError>) -> Void) {
        let request = APIRouter.Comment.getCommentList(page: page, size: size)
        APIService.shared.request(request, completion: completion)
    }

    //发送评论
    func sendComment(worksId: Int, commentDesc: String, address: String? = nil, commentImg: [String]? = nil, lat: String? = nil, lng: String? = nil, completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Comment.sendComment(worksId: worksId, commentDesc: commentDesc, address: address, commentImg: commentImg, lat: lat, lng: lng)
        APIService.shared.request(request, completion: completion)
    }

    //回复评论
    func replyComment(worksId: Int, pid: Int, commentDesc: String, address: String? = nil, commentImg: [String]? = nil, lat: String? = nil, lng: String? = nil, pcustomerId: String? = nil, completion: @escaping (Result<CommentSingleResponse, APIError>) -> Void) {
        let request = APIRouter.Comment.replyComment(worksId: worksId, pid: pid, commentDesc: commentDesc, address: address, commentImg: commentImg, lat: lat, lng: lng, pcustomerId: pcustomerId)
        APIService.shared.request(request, completion: completion)
    }

    // 删除评论
    func deleteComment(commentId: Int, completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Comment.deleteComment(commentId: commentId)
        APIService.shared.request(request, completion: completion)
    }

    //获得首页推荐视频
    func getMainWorksInfo(worksCategoryId: Int? = nil, completion: @escaping (Result<MainWorksInfoResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getMainWorksInfo(worksCategoryId: worksCategoryId)
        APIService.shared.request(request, completion: completion)
    }
    
    // MARK: - 关注作品流
    /// 获取关注用户的视频流
    /// - Parameters:
    ///   - loadNumber: 加载数量 5(首次),10(缓存<20),20(缓存<30)
    ///   - completion: 回调
    func getFollowWorksList(loadNumber: Int, completion: @escaping (Result<VideoArrayResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getFollowWorksList(loadNumber: loadNumber)
        APIService.shared.request(request, completion: completion)
    }

    // MARK: - 朋友作品流
    /// 获取朋友（互相关注）的视频流
    /// - Parameters:
    ///   - loadNumber: 加载数量，固定10条
    ///   - completion: 回调
    func getFriendWorksList(loadNumber: Int, completion: @escaping (Result<VideoArrayResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getFriendWorksList(loadNumber: loadNumber)
        APIService.shared.request(request, completion: completion)
    }

    //获得指定视频详情
    func getVideoDetail(videoId: Int, completion: @escaping (Result<VideoDetailResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getVideoDetail(videoId: videoId)
        APIService.shared.request(request, completion: completion)
    }

    //获得播放签名
    func getPlaySign(videoId: Int, completion: @escaping (Result<VodPlayerSignatureResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getPlaySign(videoId: videoId)
        APIService.shared.request(request, completion: completion)
    }

    // MARK: - 视频推流接口
    /// 获取视频推流
    /// - Parameters:
    ///   - loadNumber: 加载数量，推荐流固定10条（全部由后端计算）
    ///   - completion: 回调
    func getVideoStream(loadNumber: Int, completion: @escaping (Result<VideoArrayResponse, APIError>) -> Void) {
        // 判断本地token还在吗，不在则走getVideoStream_unLogin
        let isLoggedIn = AuthManager.shared.isLoggedIn

        // 根据登录状态选择不同的API路由
        let request: APIRequest
        if isLoggedIn {
            // 已登录：使用 /api/video/main/worksInfoListMain
            request = APIRouter.Video.getVideoStream(loadNumber: loadNumber)
            print("[APIManager] 用户已登录，使用登录视频流API: /api/video/main/worksInfoListMain")
        } else {
            // 未登录：使用 /api/video/main/worksInfoListMain.do，需要传递设备ID
            let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
            request = APIRouter.Video.getVideoStream_unLogin(deviceId: deviceId, loadNumber: loadNumber)
            print("[APIManager] 用户未登录，使用未登录视频流API: /api/video/main/worksInfoListMain.do，设备ID: \(deviceId)")
        }

        APIService.shared.request(request, completion: completion)
    }

    //获取视频评论
    func getVideoComment(
        worksId: Int,
        size: Int,
        pid: Int? = nil,
        excludeId: Int? = nil,
        fromId: Int? = nil,
        createTime: String? = nil,
        completion: @escaping (Result<CommentListResponse, APIError>) -> Void
    ) {
        let request = APIRouter.Comment.getVideoComment(
            worksId: worksId,
            size: size,
            pid: pid,
            excludeId: excludeId,
            fromId: fromId,
            createTime: createTime
        )
        APIService.shared.request(request, completion: completion)
    }

    //评论点赞，点踩
    func operateWorkComment(commentId: Int, operateType: Int, operateValue: Int, worksId: Int, completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Comment.operateWorkComment(commentId: commentId, operateType: operateType, operateValue: operateValue, worksId: worksId)
        APIService.shared.request(request, completion: completion)
    }

    // MARK: - 同城作品流
    /// 获取同城的视频流
    /// - Parameters:
    ///   - loadNumber: 加载数量，后端修改了加载方案，不需要手动控制缓存，每次请求都会由后台计算是否加载过，我们一直加载10条即可
    ///   - areaCode: 市区id
    ///   - completion: 回调
    func getCityWorksList(loadNumber: Int, areaCode: String, completion: @escaping (Result<VideoArrayResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getCityWorksList(loadNumber: loadNumber, areaCode: areaCode)
        APIService.shared.request(request, completion: completion)
    }
    
    // MARK: - 未登录同城作品流
    /// 获取未登录用户的同城视频流
    /// - Parameters:
    ///   - deviceId: 设备id
    ///   - loadNumber: 加载数量，后端修改了加载方案，不需要手动控制缓存，每次请求都会由后台计算是否加载过，我们一直加载10条即可
    ///   - areaCode: 市区id
    ///   - completion: 回调
    func getMainCityWorksList(deviceId: String, loadNumber: Int, areaCode: String, completion: @escaping (Result<VideoArrayResponse, APIError>) -> Void) {
        let request = APIRouter.Video.getMainCityWorksList(deviceId: deviceId, loadNumber: loadNumber, areaCode: areaCode)
        APIService.shared.request(request, completion: completion)
    }

    // MARK: - 观看记录
    /// 添加观看记录
    /// - Parameters:
    ///   - worksId: 作品id
    ///   - completion: 回调
    func addWorksWatch(worksId: Int, completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let request = APIRouter.Video.addWorksWatch(worksId: worksId)
        APIService.shared.request(request, completion: completion)
    }
}
