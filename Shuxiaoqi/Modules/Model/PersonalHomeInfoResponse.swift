//
//  PersonalHomeInfoResponse.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/6/4.
//

import SmartCodable

struct PersonalHomeInfoResponse: SmartCodable {
    var status: Int = 0 // 或者 Int = -1 作为默认值
    var errMsg: String?
    var msg: String?
    var data: PersonalHomeInfoData?

    // HandyJSON 需要一个空的初始化器
    init() {}

    // 可选：添加一个计算属性来判断是否成功，类似 LoginResponse
    var isSuccess: Bool {
        return status == 200
    }

    // 可选：添加一个显示消息的计算属性
    var displayMessage: String {
        if let message = msg, !message.isEmpty {
            return message
        }
        if let errorMsg = errMsg, !errorMsg.isEmpty {
            return errorMsg
        }
        return isSuccess ? "成功" : "未知错误"
    }
}

struct PersonalHomeInfoData: SmartCodable {
    var customerId: String = ""
    var nickName: String = ""
    var wxAvator: String = ""
    var customerAccount: String = ""
    var personalitySign: String? = nil
    var followNumber: Int = 0
    var likeNumber: Int = 0
    var fansNumber: Int = 0
    var worksNumber: Int = 0
    var worksLikeNumber: Int = 0
    var worksCollectNumber: Int = 0
    var follow:Bool = false
    var labels:[String] = [] //兴趣标签
    var mentionedUser: [String: String]? = nil // 新增：@用户名映射字典
    var backgroundImage: String? = nil // 新增：背景图URL

    // 计算属性：获取用户昵称，兼容新旧数据结构
    var displayNickName: String {
        // 如果 nickName 不为空，直接返回
        if !nickName.isEmpty {
            return nickName
        }
        // 如果为空，返回默认值
        return "用户名"
    }
}

