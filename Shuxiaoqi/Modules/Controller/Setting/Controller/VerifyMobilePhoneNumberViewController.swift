//
//  VerifyMobilePhoneNumberViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/4/12.
//

import Foundation
import UIKit
import SnapKit // Import SnapKit

// 定义验证类型枚举
enum VerificationType: String {
    case changePassword = "changePwd"       // 修改密码前的验证旧手机
    case verifyOldPhoneForBind = "verifyOldPhone" // 换绑手机时验证旧手机
    case bindPwd = "bindPwd"         // 忘记密码时使用
    case deleteAccount = "deleteAccount"    // 注销账号时验证手机号
    // 可以根据需要添加更多类型，例如注销账号等
}

class VerifyMobilePhoneNumberViewController: BaseViewController {

    // MARK: - Properties
    // 新增: 验证类型，默认为修改密码
    var verificationType: VerificationType = .changePassword
    // 新增: 图形验证码 ID
    private var codeId: String = ""
    // 可选: 如果是从上个页面传来的旧手机号，用于显示或预填
    var currentPhoneNumber: String?

    // 注销账号相关属性
    var deleteAccountReason: String? // 注销原因
    var deleteAccountType: Int = 0   // 注销类型
    var deleteAccountRemark: String? // 注销备注

    // MARK: - UI 组件 (Referencing PhoneBindingViewController)

    // 手机号输入框容器
    private lazy var phoneInputContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 手机号输入框
    private lazy var phoneTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "请输入手机号码"
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.backgroundColor = .white
        textField.textColor = UIColor(hex: "#000000", alpha: 0.45)
        textField.keyboardType = .numberPad
        return textField
    }()

    // 手机号下划线
    private lazy var phoneUnderline: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#000000", alpha: 0.1)
        return view
    }()

    // 图形验证码输入框容器
    private lazy var verificationCodeContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 图形验证码输入框
    private lazy var verificationCodeTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "请输入图形验证码"
        textField.backgroundColor = .white
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = UIColor(hex: "#333333")
        return textField
    }()

    // 图形验证码下划线
    private lazy var verificationUnderline: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#000000", alpha: 0.1)
        return view
    }()

    // 图形验证码图片
    private lazy var captchaImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.backgroundColor = UIColor(hex: "#EEEEEE") // Placeholder color
        imageView.contentMode = .scaleAspectFit
        imageView.clipsToBounds = true
        imageView.isUserInteractionEnabled = true

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(refreshCaptcha))
        imageView.addGestureRecognizer(tapGesture)

        return imageView
    }()

    // 短信验证码输入框容器
    private lazy var smsCodeContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 短信验证码输入框
    private lazy var smsCodeTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "请输入验证码"
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.backgroundColor = .white
        textField.textColor = UIColor(hex: "#333333")
        textField.keyboardType = .numberPad
        return textField
    }()

    // 短信验证码下划线
    private lazy var smsUnderline: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#000000", alpha: 0.1)
        return view
    }()

    // 获取验证码按钮
    private lazy var getSmsCodeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("获取验证码", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor(hex: "#FFA245") // Orange color from image
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.layer.cornerRadius = 16 // Make it rounded
        button.addTarget(self, action: #selector(getSmsCodeButtonTapped), for: .touchUpInside)
        return button
    }()

    // 下一步按钮
    private lazy var nextButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("下一步", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = UIColor(hex: "#FF8F1F") // Orange color from image
        button.layer.cornerRadius = 6 // Slightly rounded corners
        button.addTarget(self, action: #selector(nextButtonTapped), for: .touchUpInside)
        return button
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        contentView.backgroundColor = .white
        setupUI()
        configureUIForType() // 根据类型配置 UI

        // Add tap gesture to dismiss keyboard
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        tapGesture.cancelsTouchesInView = false
        view.addGestureRecognizer(tapGesture)

        // Load initial captcha
        refreshCaptcha()
        
        // **新增**: 获取并设置脱敏手机号 (如果需要)
        fetchDesensitizedPhoneNumber()
    }

    // MARK: - UI 设置
    private func setupUI() {
        // Add phone number input
        contentView.addSubview(phoneInputContainer)
        phoneInputContainer.snp.makeConstraints { make in
            // Adjust top offset as there is no subtitle
            make.top.equalTo(navBar.snp.bottom).offset(24)
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-16)
            make.height.equalTo(60)
        }
        phoneInputContainer.addSubview(phoneTextField)
        phoneTextField.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        phoneInputContainer.addSubview(phoneUnderline)
        phoneUnderline.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(1)
        }

        // Add captcha input
        contentView.addSubview(verificationCodeContainer)
        verificationCodeContainer.snp.makeConstraints { make in
            make.top.equalTo(phoneInputContainer.snp.bottom)
            make.leading.trailing.equalTo(phoneInputContainer)
            make.height.equalTo(60)
        }
        verificationCodeContainer.addSubview(verificationCodeTextField)
        verificationCodeTextField.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-122) // Space for image
            make.centerY.equalToSuperview()
        }
        verificationCodeContainer.addSubview(captchaImageView)
        captchaImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(90)
            make.height.equalTo(30)
        }
        verificationCodeContainer.addSubview(verificationUnderline)
        verificationUnderline.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(1)
        }

        // Add SMS code input
        contentView.addSubview(smsCodeContainer)
        smsCodeContainer.snp.makeConstraints { make in
            make.top.equalTo(verificationCodeContainer.snp.bottom)
            make.leading.trailing.equalTo(phoneInputContainer)
            make.height.equalTo(60)
        }
        smsCodeContainer.addSubview(smsCodeTextField)
        smsCodeTextField.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-122) // Space for button
            make.centerY.equalToSuperview()
        }
        smsCodeContainer.addSubview(getSmsCodeButton)
        getSmsCodeButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(90)
            make.height.equalTo(32)
        }
        smsCodeContainer.addSubview(smsUnderline)
        smsUnderline.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(1)
        }

        // Add Next button
        contentView.addSubview(nextButton)
        nextButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-16)
            // Position above safe area bottom
            make.bottom.equalTo(contentView.safeAreaLayoutGuide.snp.bottom).offset(-20)
            make.height.equalTo(44)
        }
    }

    // 新增：根据类型配置特定UI文本
    private func configureUIForType() {
        switch verificationType {
        case .changePassword, .verifyOldPhoneForBind, .deleteAccount:
            // 这些类型需要验证当前账号手机号，使用脱敏手机号
            if verificationType == .deleteAccount {
                navTitle = "验证手机号"
                phoneTextField.placeholder = "当前绑定手机号"
                nextButton.setTitle("下一步", for: .normal)
            } else {
                navTitle = "验证手机号"
                phoneTextField.placeholder = "当前绑定手机号"
                nextButton.setTitle("下一步", for: .normal)
            }
            phoneTextField.isEnabled = false // **修改点**: 默认禁用
            phoneTextField.textColor = .gray // **修改点**: 视觉上提示禁用
         case .bindPwd:
             navTitle = "忘记密码"
             phoneTextField.placeholder = "请输入手机号"
             nextButton.setTitle("下一步", for: .normal)
             phoneTextField.isEnabled = true // 忘记密码时允许输入
             phoneTextField.textColor = UIColor(hex: "#333333") // 恢复默认颜色
        }
    }
    
    // **新增**: 获取并设置脱敏手机号 (如果需要)
    private func fetchDesensitizedPhoneNumber() {
        // 仅在需要验证当前手机号的场景下执行，忘记密码场景(.bindPwd)下不执行
        guard verificationType == .changePassword || verificationType == .verifyOldPhoneForBind || verificationType == .deleteAccount else {
            // 如果是忘记密码场景，确保手机号输入框可用
            if verificationType == .bindPwd {
                self.phoneTextField.isEnabled = true
                self.phoneTextField.textColor = UIColor(hex: "#333333")
            }
            return
        }

        print("调用 API：获取当前用户脱敏手机号...")
        // 先禁用输入框和设置颜色
        self.phoneTextField.isEnabled = false
        self.phoneTextField.textColor = .gray

        APIManager.shared.getUserPhone { [weak self] result in // 开始闭包
            guard let self = self else { return } // 捕获 self

            DispatchQueue.main.async { // 切换到主线程处理结果和 UI
                // 在主线程中处理 result
                switch result {
                case .success(let response):
                    // 修改点：直接使用 response.data，它是 String? 类型
                    if response.isSuccess, let desensitizedPhone = response.data?.phone, !desensitizedPhone.isEmpty {
                        print("获取到脱敏手机号: \(desensitizedPhone)")
                        self.phoneTextField.text = desensitizedPhone
                    } else {
                        // 处理获取失败或 data 为空的情况
                        let message = response.displayMessage // 使用 displayMessage 获取错误信息
                        print("获取脱敏手机号失败或为空。Msg: \(message)")
                        self.phoneTextField.text = "未能获取手机号"
                        self.showToast(message.isEmpty ? "未能获取您的手机号" : message)
                    }
                case .failure(let error):
                    print("获取脱敏手机号网络或解析失败: \(error.errorMessage)")
                    self.phoneTextField.text = "获取失败"
                    self.showToast("获取手机号失败: \(error.errorMessage)")
                }

                // 确保 UI 状态最终正确 (即使 API 调用失败也要保持禁用)
                self.phoneTextField.isEnabled = false
                self.phoneTextField.textColor = .gray

                // 检查 currentPhoneNumber 是否存在
                if self.currentPhoneNumber == nil || self.currentPhoneNumber!.isEmpty {
                     print("警告：未提供真实的 currentPhoneNumber，后续的短信验证等操作可能失败。请确保在调用此页面前设置了 currentPhoneNumber。")
                }
            } // 结束 DispatchQueue.main.async
        } // 结束 API 调用闭包
    }

    // MARK: - 事件响应方法

    @objc private func refreshCaptcha() {
        print("VerifyMobile: 刷新图形验证码 (Type: \(verificationType.rawValue))")
        captchaImageView.image = nil
        captchaImageView.subviews.forEach { $0.removeFromSuperview() }

        let activityIndicator = UIActivityIndicatorView(style: .medium)
        activityIndicator.translatesAutoresizingMaskIntoConstraints = false
        captchaImageView.addSubview(activityIndicator)
        NSLayoutConstraint.activate([
            activityIndicator.centerXAnchor.constraint(equalTo: captchaImageView.centerXAnchor),
            activityIndicator.centerYAnchor.constraint(equalTo: captchaImageView.centerYAnchor)
        ])
        activityIndicator.startAnimating()

        let timestamp = Int(Date().timeIntervalSince1970 * 1000)
        let random1 = Int.random(in: 0...9)
        let random2 = Int.random(in: 0...9)
        self.codeId = "\(timestamp)\(random1)\(random2)"
        print("VerifyMobile: Generated new codeId: \(self.codeId)")
        var typeStr = ""
        switch verificationType {
        case .changePassword, .verifyOldPhoneForBind, .deleteAccount:
            typeStr = "common"
            break
        case .bindPwd:
            typeStr = "bindPwd"
            break
        }
        // **修改点**: 使用 verificationType.rawValue 作为 API 的 type 参数
        APIManager.shared.getCaptcha(codeId: self.codeId, type: typeStr) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                activityIndicator.removeFromSuperview()
                switch result {
                case .success(let image):
                    if let img = image {
                        self.captchaImageView.image = img
                    } else {
                        // self.showToast(message: "无法显示验证码图片")
                        self.captchaImageView.image = UIImage(systemName: "exclamationmark.triangle")
                    }
                case .failure(let error):
                    // self.showToast(message: error.errorMessage)
                    self.captchaImageView.image = UIImage(systemName: "exclamationmark.triangle")
                    print("获取验证码失败: \(error.errorMessage)") // Log error
                }
            }
        }
    }

    @objc private func getSmsCodeButtonTapped() {
        print("VerifyMobile: 获取短信验证码 (Type: \(verificationType.rawValue))")

        // 检查验证码输入
        guard let captcha = verificationCodeTextField.text, !captcha.isEmpty else {
            print("请输入图形验证码")
            showToast("请输入图形验证码")
            return
        }
        
        guard !self.codeId.isEmpty else {
            print("CodeID 为空，请先刷新图形验证码")
            showToast("请先刷新图形验证码")
            return
        }
        
        // 禁用按钮，防止重复点击
        getSmsCodeButton.isEnabled = false
        getSmsCodeButton.backgroundColor = UIColor.lightGray
        
        // 根据不同的验证类型调用不同的接口
        switch verificationType {
        case .bindPwd:
            // 忘记密码场景需要验证用户输入的手机号
            guard let phone = phoneTextField.text, !phone.isEmpty else {
                print("请输入手机号")
                showToast("请输入手机号")
                getSmsCodeButton.isEnabled = true
                getSmsCodeButton.backgroundColor = UIColor(hex: "#FFA245")
                return
            }
            
            // 验证手机号格式
            if !isValidPhone(phone) {
                print("请输入正确的手机号码")
                showToast("请输入正确的手机号码")
                getSmsCodeButton.isEnabled = true
                getSmsCodeButton.backgroundColor = UIColor(hex: "#FFA245")
                return
            }
            
            // 打印当前导航状态
            printNavigationState("VerifyMobilePhoneNumberViewController -> 忘记密码验证")
            
            // 调用忘记密码专用接口
            print("调用忘记密码API验证: phone=\(phone), smsCode=\(captcha), codeId=\(codeId)")
            
            APIManager.shared.getSMSCode(phone: phone, captchaCode: captcha, codeId: codeId, type: "bindPwd") { [weak self] result in
                guard let self = self else { return }
                
                DispatchQueue.main.async {
                    switch result {
                    case .success(let response):
                        if response.isSuccess {
                            print("获取忘记密码验证码成功")
                            self.showToast("验证码已发送")
                            self.startCountdown()
                        } else {
                            print("获取忘记密码验证码失败: \(response.displayMessage)")
                            self.showToast(response.displayMessage)
                            self.getSmsCodeButton.isEnabled = true
                            self.getSmsCodeButton.backgroundColor = UIColor(hex: "#FFA245")
                            self.refreshCaptcha() // 刷新验证码
                        }
                    case .failure(let error):
                        print("获取忘记密码验证码错误: \(error.errorMessage)")
                        self.showToast(error.errorMessage)
                        self.getSmsCodeButton.isEnabled = true
                        self.getSmsCodeButton.backgroundColor = UIColor(hex: "#FFA245")
                        self.refreshCaptcha() // 刷新验证码
                    }
                }
            }
            
        case .changePassword, .verifyOldPhoneForBind, .deleteAccount:
            // 原有的getCommonMSG接口逻辑
            print("调用 getCommonMSG 接口获取通用验证码: captcha=\(captcha), codeId=\(codeId)")

            APIManager.shared.getCommonMSG(codeId: self.codeId, captchaCode: captcha) { [weak self] result in
                guard let self = self else { return }

                DispatchQueue.main.async {
                    switch result {
                    case .success(let response):
                        if response.isSuccess {
                            print("获取通用验证码成功")
                            self.showToast("验证码已发送")
                            self.startCountdown()
                        } else {
                            print("获取通用验证码失败: \(response.displayMessage)")
                            self.showToast(response.displayMessage)
                            self.getSmsCodeButton.isEnabled = true
                            self.getSmsCodeButton.backgroundColor = UIColor(hex: "#FFA245")
                            self.refreshCaptcha() // 刷新验证码
                        }
                    case .failure(let error):
                        print("获取通用验证码错误: \(error.errorMessage)")
                        self.showToast(error.errorMessage)
                        self.getSmsCodeButton.isEnabled = true
                        self.getSmsCodeButton.backgroundColor = UIColor(hex: "#FFA245")
                        self.refreshCaptcha() // 刷新验证码
                    }
                }
            }


        }
    }

    @objc private func nextButtonTapped() {
        print("VerifyMobile: 下一步按钮点击 (Type: \(verificationType.rawValue))")
        
        guard let smsCode = smsCodeTextField.text, !smsCode.isEmpty else {
            print("请输入短信验证码")
            showToast("请输入短信验证码")
            return
        }
        
        // 禁用按钮防止重复点击
        nextButton.isEnabled = false

        // 针对忘记密码场景，需要检查输入的手机号
        if verificationType == .bindPwd {
            guard let phone = phoneTextField.text, !phone.isEmpty else {
                print("请输入手机号码")
                showToast("请输入手机号码")
                nextButton.isEnabled = true
                return
            }
            
            // 验证手机号格式
            if !isValidPhone(phone) {
                print("请输入正确的手机号码")
                showToast("请输入正确的手机号码")
                nextButton.isEnabled = true
                return
            }
            
            // 打印当前导航状态
            printNavigationState("VerifyMobilePhoneNumberViewController -> 忘记密码验证")
            
            // 调用忘记密码专用接口
            print("调用忘记密码API验证: phone=\(phone), smsCode=\(smsCode)")
            
            APIManager.shared.checkUserPwdMsg(phone: phone, code: smsCode) { [weak self] result in
                guard let self = self else { return }
                
                DispatchQueue.main.async {
                    // 恢复按钮状态
                    self.nextButton.isEnabled = true
                    
                    switch result {
                    case .success(let response):
                        if response.isSuccess {
                            print("忘记密码验证成功，准备跳转到设置新密码页面")
                            
                            // 创建设置新密码页面
                            let setNewPasswordVC = SetNewPasswordViewController()
                            setNewPasswordVC.phone = phone // 传递手机号
                            if let checkId = response.data?.checkId {
                                print("获取到checkId: \(checkId)，将传递给设置密码页面")
                                setNewPasswordVC.checkId = checkId
                            } else {
                                print("警告：API返回的checkId为空")
                            }
                            
                            // 保存当前导航信息供返回时使用
                            self.printNavigationState("准备跳转到SetNewPassword")
                            
                            // 选择合适的跳转方式
                            if let navController = self.navigationController {
                                print("找到navigationController，使用pushViewController")
                                navController.pushViewController(setNewPasswordVC, animated: true)
                            } else {
                                print("未找到navigationController，尝试使用模态方式")
                                // 如果没有导航控制器，尝试模态方式
                                setNewPasswordVC.modalPresentationStyle = .fullScreen
                                self.present(setNewPasswordVC, animated: true, completion: nil)
                            }
                        } else {
                            print("忘记密码验证失败: \(response.displayMessage)")
                            self.showToast(response.displayMessage)
                            self.refreshCaptcha() // 刷新图形验证码
                        }
                    case .failure(let error):
                        print("忘记密码验证网络错误: \(error.errorMessage)")
                        self.showToast(error.errorMessage)
                        self.refreshCaptcha() // 刷新图形验证码
                    }
                }
            }
            return
        }

        // 针对注销账号场景，直接使用短信验证码调用注销接口
        if verificationType == .deleteAccount {
            print("注销账号：直接使用短信验证码调用注销接口")
            self.showDeleteAccountConfirmation(smsCode: smsCode)
            nextButton.isEnabled = true
            return
        }

        // 原有逻辑保持不变，仅在修改密码和验证旧手机场景下执行
        APIManager.shared.checkCommonMsg(code: smsCode) { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                // 恢复按钮状态
                self.nextButton.isEnabled = true
                
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        print("通用验证码验证成功。")
                        // --- 验证成功后，根据类型执行后续操作 ---
                        switch self.verificationType {
                        case .changePassword:
                            print("验证成功，跳转到设置新密码页面")
                            let setNewPasswordVC = SetNewPasswordViewController()
                            setNewPasswordVC.checkId = response.data?.checkId ?? ""
                            self.navigationController?.pushViewController(setNewPasswordVC, animated: true)
                        case .verifyOldPhoneForBind:
                            print("旧手机验证成功，跳转到绑定新手机页面")
                            let bindNewPhoneVC = PhoneBindingViewController()
                            bindNewPhoneVC.checkId = response.data?.checkId ?? ""
                            self.navigationController?.pushViewController(bindNewPhoneVC, animated: true)
                        case .deleteAccount:
                            // 不应该走到这个分支，因为注销账号已在前面单独处理
                            print("警告：deleteAccount分支不应该被执行")
                        case .bindPwd:
                            // 不应该走到这个分支，因为已在前面处理
                            print("警告：bindPwd分支不应该被执行")
                        }
                    } else {
                        print("通用验证码验证失败: \(response.displayMessage)")
                        self.showToast(response.displayMessage)
                        self.refreshCaptcha()
                    }
                case .failure(let error):
                    print("通用验证码验证网络错误: \(error.errorMessage)")
                    self.showToast("验证出错: \(error.errorMessage)")
                    self.refreshCaptcha()
                }
            }
        }
    }

    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }

    // MARK: - 辅助方法

    private func startCountdown() {
        var seconds = 60
        getSmsCodeButton.isEnabled = false
        getSmsCodeButton.setTitle("\(seconds)秒后重试", for: .disabled)
        getSmsCodeButton.setTitleColor(.white, for: .disabled)
        getSmsCodeButton.backgroundColor = .lightGray // Disabled color

        let timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            guard let self = self else {
                timer.invalidate()
                return
            }
            seconds -= 1
            self.getSmsCodeButton.setTitle("\(seconds)秒后重试", for: .disabled)

            if seconds <= 0 {
                timer.invalidate()
                self.getSmsCodeButton.isEnabled = true
                self.getSmsCodeButton.setTitle("获取验证码", for: .normal)
                self.getSmsCodeButton.backgroundColor = UIColor(hex: "#FFA245") // Restore original color
            }
        }
        RunLoop.current.add(timer, forMode: .common)
    }

    // 验证手机号格式的辅助方法
    private func isValidPhone(_ phone: String) -> Bool {
        let pattern = "^1[3-9]\\d{9}$"
        let predicate = NSPredicate(format: "SELF MATCHES %@", pattern)
        return predicate.evaluate(with: phone)
    }

    // 添加打印导航状态的辅助方法
    private func printNavigationState(_ context: String) {
        print("\n===== 导航状态 [\(context)] =====")
        print("当前视图控制器: \(type(of: self))")
        print("导航控制器: \(self.navigationController != nil ? "存在" : "不存在")")
        
        if let navController = self.navigationController {
            print("导航栈内容: \(navController.viewControllers.map { type(of: $0) })")
            print("当前页在导航栈的位置: \(navController.viewControllers.firstIndex(of: self) ?? -1)")
        }
        
        print("呈现方式: \(self.presentingViewController != nil ? "Modal呈现" : "非Modal呈现")")
        if let presenter = self.presentingViewController {
            print("呈现者类型: \(type(of: presenter))")
            
            // 如果呈现者是导航控制器，检查其导航栈
            if let navController = presenter as? UINavigationController {
                print("呈现者导航栈内容: \(navController.viewControllers.map { type(of: $0) })")
            }
        }
        
        print("Modal显示的控制器: \(self.presentedViewController != nil ? "\(type(of: self.presentedViewController!))" : "无")")
        print("===== 导航状态结束 =====\n")
    }

    // MARK: - 公共方法

    /// 刷新脱敏手机号（供外部调用，比如更换手机号成功后）
    func refreshDesensitizedPhoneNumber() {
        fetchDesensitizedPhoneNumber()
    }

    // MARK: - 注销账号相关方法

    /// 显示注销账号二次确认弹窗
    private func showDeleteAccountConfirmation(smsCode: String) {
        let alertController = UIAlertController(
            title: "确认注销账号",
            message: "注销后您的所有数据将被永久删除且无法恢复，确定要继续吗？",
            preferredStyle: .alert
        )

        // 取消按钮
        let cancelAction = UIAlertAction(title: "取消", style: .cancel) { [weak self] _ in
            // 恢复按钮状态
            self?.nextButton.isEnabled = true
        }
        alertController.addAction(cancelAction)

        // 确认注销按钮
        let confirmAction = UIAlertAction(title: "确认注销", style: .destructive) { [weak self] _ in
            self?.performDeleteAccount(smsCode: smsCode)
        }
        alertController.addAction(confirmAction)

        present(alertController, animated: true)
    }

    /// 执行注销账号API调用
    private func performDeleteAccount(smsCode: String) {
        print("开始执行注销账号API调用")

        // 显示加载状态
        nextButton.isEnabled = false

        APIManager.shared.logoutUser(
            logoutCode: smsCode,
            logoutRemark: deleteAccountRemark ?? "",
            logoutType: deleteAccountType
        ) { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        print("注销账号成功")
                        self.showSuccessAndNavigateToHome()
                    } else {
                        print("注销账号失败: \(response.displayMessage)")
                        self.showToast(response.displayMessage)
                        self.nextButton.isEnabled = true
                    }
                case .failure(let error):
                    print("注销账号网络错误: \(error.errorMessage)")
                    self.showToast("注销失败: \(error.errorMessage)")
                    self.nextButton.isEnabled = true
                }
            }
        }
    }

    /// 显示成功提示并导航到首页
    private func showSuccessAndNavigateToHome() {
        // 显示成功提示
        showToast("注销成功")

        // 清理本地数据
        AuthManager.shared.logout()

        // 2秒后导航到首页
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.navigateToHome()
        }
    }

    /// 导航到首页
    private func navigateToHome() {
        // 获取主窗口的根视图控制器
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let tabBarController = window.rootViewController as? UITabBarController else {
            print("无法获取TabBarController")
            return
        }

        // 切换到首页tab（通常是index 0）
        tabBarController.selectedIndex = 0

        // 如果当前有导航控制器，返回到根视图控制器
        if let navController = tabBarController.selectedViewController as? UINavigationController {
            navController.popToRootViewController(animated: false)
        }

        // 关闭当前的模态视图或导航栈
        if let presentingVC = self.presentingViewController {
            presentingVC.dismiss(animated: true, completion: nil)
        } else if let navController = self.navigationController {
            navController.popToRootViewController(animated: true)
        }
    }

    // Optional: Add showToast method if needed, or use an existing one
    // private func showToast(message: String) { ... }
}
