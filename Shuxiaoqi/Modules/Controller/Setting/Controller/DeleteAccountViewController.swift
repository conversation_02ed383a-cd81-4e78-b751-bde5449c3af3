//
//  DeleteAccountViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/3/20.
//

import UIKit
import SnapKit

// 注销原因选项
enum DeleteAccountReason: String, CaseIterable {
    case platformFeatures = "平台功能不满足需求"
    case costTooHigh = "费用过高"
    case customerService = "客服服务不满意"
    case businessAdjustment = "业务调整"
    case other = "其他原因"
    
    var displayText: String {
        return self.rawValue
    }
}

class DeleteAccountViewController: BaseViewController, UITextViewDelegate {
    
    // 选中的注销原因
    private var selectedReason: DeleteAccountReason?
    
    // 重要提示容器视图
    private lazy var tipContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#FAF6F4")
        view.layer.cornerRadius = 12
        return view
    }()
    
    // 提示图标
    private lazy var tipIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "information-square")
        imageView.tintColor = UIColor(hex: "#FF9500")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 提示标题
    private lazy var tipTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "重要提示"
        label.font = UIFont.systemFont(ofSize: 13, weight: .medium)
        label.textColor = UIColor(hex: "#1F1D1D")
        return label
    }()
    
    // 提示内容
    private lazy var tipContentLabel: UILabel = {
        let label = UILabel()
        label.text = "注销账号将永久删除您的商家信息和所有相关数据，此操作不可撤销。"
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#E87422")
        label.numberOfLines = 0
        return label
    }()
    
    // 注销原因标题
    private lazy var reasonTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "请告诉我们注销原因"
        label.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        label.textColor = UIColor(hex: "#1F1D1D")
        return label
    }()
    
    // 注销协议标题
    private lazy var agreementTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "注销协议"
        label.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        label.textColor = UIColor(hex: "#1F1D1D")
        return label
    }()
    
    // 协议内容容器
    private lazy var agreementContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 协议内容堆栈视图
    private lazy var agreementStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.distribution = .fill
        stackView.alignment = .fill
        return stackView
    }()
    
    
    // 添加渐变层属性
    private let verifyGradientLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.startPoint = CGPoint(x: 0, y: 0.5)
        layer.endPoint = CGPoint(x: 1, y: 0.5)
        return layer
    }()
    
    // 添加验证按钮
    private lazy var verifyButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("验证身份并继续", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = .clear
        button.layer.cornerRadius = 8
        button.clipsToBounds = true
        button.isEnabled = false
        button.addTarget(self, action: #selector(verifyButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.isUserInteractionEnabled = true // 启用用户交互
        scrollView.showsVerticalScrollIndicator = false
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()
    
    // 内容容器视图
    private lazy var contentContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.isUserInteractionEnabled = true // 确保启用用户交互
        return view
    }()
    
    // 注销原因选项按钮数组
    private var reasonButtons: [UIButton] = []
    
    // MARK: - 其他原因文本输入
    
    // 输入容器
    private lazy var otherReasonContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        view.layer.cornerRadius = 8
        view.clipsToBounds = true
        view.isHidden = true // 默认隐藏，选择"其他原因"时显示
        return view
    }()
    
    // 文本输入框
    private lazy var otherReasonTextView: UITextView = {
        let tv = UITextView()
        tv.font = UIFont.systemFont(ofSize: 14)
        tv.textColor = UIColor(hex: "#1F1D1D")
        tv.backgroundColor = .clear
        tv.delegate = self
        tv.textContainerInset = UIEdgeInsets(top: 12, left: 10, bottom: 24, right: 10)
        return tv
    }()
    
    // 占位提示
    private lazy var placeholderLabel: UILabel = {
        let label = UILabel()
        label.text = "请输入详细原因"
        label.textColor = UIColor(hex: "#AFAFAF")
        label.font = UIFont.systemFont(ofSize: 14)
        return label
    }()
    
    // 字数统计
    private lazy var charCountLabel: UILabel = {
        let label = UILabel()
        label.text = "0/200"
        label.textColor = UIColor(hex: "#999999")
        label.font = UIFont.systemFont(ofSize: 12)
        return label
    }()
    
    // 容器高度约束（用于动态展开/收起）
    private var otherReasonHeightConstraint: Constraint? = nil
    
    // 添加协议勾选相关属性
    private var isAgreed: Bool = false
    
    // 复选框图标
    private lazy var checkboxImageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "checkbox_unchecked"))
        imageView.contentMode = .scaleAspectFit
        imageView.isUserInteractionEnabled = false
        return imageView
    }()
    
    // 协议文本标签
    private lazy var agreementLabel: UILabel = {
        let label = UILabel()
        label.text = "我已阅读并同意上述注销协议"
        label.textColor = UIColor(hex: "#1F1D1D")
        label.font = UIFont.systemFont(ofSize: 14)
        return label
    }()
    
    // 协议勾选容器视图
    private lazy var agreementCheckContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.isUserInteractionEnabled = true
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(agreementCheckTapped))
        view.addGestureRecognizer(tapGesture)
        
        return view
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置标题
        navTitle = "注销账户"
        self.contentView.backgroundColor = .white
        
        // 设置视图
        setupViews()
        
        // 创建注销原因选项
        setupReasonOptions()
        
        // 设置协议内容
        setupAgreementContent()
        
        // 键盘通知
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShow(_:)), name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillHide(_:)), name: UIResponder.keyboardWillHideNotification, object: nil)
    }
    
    // 添加 viewDidAppear 方法
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 确保在视图完全显示后更新一次滚动视图内容大小
        DispatchQueue.main.async {
            self.updateScrollViewContentSize()
            
            // 打印视图层次结构，用于调试
            print("视图层次结构:")
            print("scrollView.isUserInteractionEnabled: \(self.scrollView.isUserInteractionEnabled)")
            print("contentContainerView.isUserInteractionEnabled: \(self.contentContainerView.isUserInteractionEnabled)")
            
            // 检查按钮是否可以接收用户交互
            for (index, button) in self.reasonButtons.enumerated() {
                print("按钮\(index).isUserInteractionEnabled: \(button.isUserInteractionEnabled)")
            }
            
//            print("agreementCheckButton.isUserInteractionEnabled: \(self.agreementCheckButton.isUserInteractionEnabled)")
        }
    }
    
    // 取消通知
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // 设置视图布局
    private func setupViews() {
        
        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加内容容器
        scrollView.addSubview(contentContainerView)
        contentContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        // 确保所有视图都启用了用户交互
        contentView.isUserInteractionEnabled = true
        scrollView.isUserInteractionEnabled = true
        contentContainerView.isUserInteractionEnabled = true
        
        // 添加提示容器
        contentContainerView.addSubview(tipContainerView)
        tipContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(14)
            make.left.equalToSuperview().offset(14)
            make.right.equalToSuperview().offset(-14)
            make.height.equalTo(81)
        }
        
        // 添加提示图标
        tipContainerView.addSubview(tipIconImageView)
        tipIconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(10)
            make.top.equalToSuperview().offset(12)
            make.width.height.equalTo(17)
        }
        
        // 添加提示标题
        tipContainerView.addSubview(tipTitleLabel)
        tipTitleLabel.snp.makeConstraints { make in
            make.left.equalTo(tipIconImageView.snp.right).offset(6)
            make.height.equalTo(17)
            make.centerY.equalTo(tipIconImageView)
        }
        
        // 添加提示内容
        tipContainerView.addSubview(tipContentLabel)
        tipContentLabel.snp.makeConstraints { make in
            make.top.equalTo(tipIconImageView.snp.bottom).offset(6)
            make.left.equalToSuperview().offset(10 )
            make.right.equalToSuperview().offset(-10)
            make.bottom.equalToSuperview().offset(-10)
        }
        
        // 添加注销原因标题
        contentContainerView.addSubview(reasonTitleLabel)
        reasonTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(tipContainerView.snp.bottom).offset(24)
            make.left.equalToSuperview().offset(29)
            make.right.equalToSuperview().offset(-29)
        }
        
        // 添加协议标题
        contentContainerView.addSubview(agreementTitleLabel)
        agreementTitleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(29)
            make.right.equalToSuperview().offset(-29)
            // 约束会在setupReasonOptions后设置
        }
        
        // 添加协议容器
        contentContainerView.addSubview(agreementContainerView)
        agreementContainerView.snp.makeConstraints { make in
            make.top.equalTo(agreementTitleLabel.snp.bottom).offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            // 底部约束会在setupAgreementContent后设置
        }
        
        // 添加协议堆栈视图
        agreementContainerView.addSubview(agreementStackView)
        agreementStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16))
        }
        
        // 添加协议勾选容器
        contentContainerView.addSubview(agreementCheckContainer)
        agreementCheckContainer.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(14)
            make.right.equalToSuperview().offset(-14)
            make.top.equalTo(agreementContainerView.snp.bottom).offset(16)
            make.height.equalTo(24)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        // 添加复选框图标
        agreementCheckContainer.addSubview(checkboxImageView)
        checkboxImageView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(18)
        }
        
        // 添加协议文本标签
        agreementCheckContainer.addSubview(agreementLabel)
        agreementLabel.snp.makeConstraints { make in
            make.left.equalTo(checkboxImageView.snp.right).offset(8)
            make.centerY.equalToSuperview()
            make.right.equalToSuperview()
        }
        
        // 将验证按钮固定在屏幕底部（不随滚动）
        contentView.addSubview(verifyButton)
        verifyButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(14)
            make.right.equalToSuperview().offset(-14)
            make.height.equalTo(42)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-12)
        }
        
        // 确保滚动视图不会延伸到验证按钮下方
        // 不需要重新设置约束，只需调整内容内边距
        scrollView.contentInset.bottom = 42 + 12 // 按钮高度 + 安全距离
        
        // 在约束设置后添加渐变层
        verifyButton.layer.insertSublayer(verifyGradientLayer, at: 0)
        updateVerifyButtonGradient(enabled: false) // 初始灰色
    }
    
    // 设置注销原因选项
    private func setupReasonOptions() {
        var lastOptionButton: UIButton?
        
        for (index, reason) in DeleteAccountReason.allCases.enumerated() {
            let optionButton = createReasonOptionButton(reason: reason, tag: index)
            contentContainerView.addSubview(optionButton)
            
            optionButton.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(16)
                make.right.equalToSuperview().offset(-16)
                make.height.equalTo(50)
                
                if let lastButton = lastOptionButton {
                    make.top.equalTo(lastButton.snp.bottom).offset(1) // 1像素分隔线
                } else {
                    make.top.equalTo(reasonTitleLabel.snp.bottom).offset(16)
                }
            }
            
            lastOptionButton = optionButton
            reasonButtons.append(optionButton)
        }
        
        // 设置协议标题的顶部约束
        if let lastButton = lastOptionButton {
            agreementTitleLabel.snp.makeConstraints { make in
                make.top.equalTo(lastButton.snp.bottom).offset(24)
            }
        }
        
        // 添加文本输入容器（默认高度 0，隐藏）
        contentContainerView.addSubview(otherReasonContainerView)
        otherReasonContainerView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            if let lastOption = lastOptionButton {
                make.top.equalTo(lastOption.snp.bottom)
            } else {
                make.top.equalTo(reasonTitleLabel.snp.bottom).offset(16)
            }
            otherReasonHeightConstraint = make.height.equalTo(0).constraint // 动态调整
        }
        
        // 在容器中添加文本视图 & 计数
        otherReasonContainerView.addSubview(otherReasonTextView)
        otherReasonTextView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20) // 留出空间给字数统计标签
        }
        
        otherReasonContainerView.addSubview(placeholderLabel)
        placeholderLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(14)
        }
        
        otherReasonContainerView.addSubview(charCountLabel)
        charCountLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-10)
            make.bottom.equalToSuperview().offset(-6)
        }
        
        // 调整协议标题顶部约束指向 otherReasonContainerView
        agreementTitleLabel.snp.remakeConstraints { make in
            make.top.equalTo(otherReasonContainerView.snp.bottom).offset(24)
            make.left.equalToSuperview().offset(29)
            make.right.equalToSuperview().offset(-29)
        }
    }
    
    // 创建注销原因选项按钮
    private func createReasonOptionButton(reason: DeleteAccountReason, tag: Int) -> UIButton {
        let button = UIButton(type: .custom)
        button.backgroundColor = .white
        button.contentHorizontalAlignment = .left
        button.contentEdgeInsets = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 0)
        button.setTitle(reason.displayText, for: .normal)
        button.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.tag = tag
        
        // 添加选择指示器
        let indicatorImageView = UIImageView()
        indicatorImageView.image = UIImage(systemName: "circle")
        indicatorImageView.tintColor = UIColor(hex: "#CCCCCC")
        indicatorImageView.contentMode = .scaleAspectFit
        indicatorImageView.tag = 100 // 用于后续查找
        indicatorImageView.isUserInteractionEnabled = false // 确保图标不会阻止按钮点击
        
        button.addSubview(indicatorImageView)
        indicatorImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        // 添加底部分割线
        let separator = UIView()
        separator.backgroundColor = UIColor(hex: "#F0EFEE")
        separator.isUserInteractionEnabled = false // 确保分隔线不会阻止按钮点击
        button.addSubview(separator)
        separator.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(13)
            make.right.equalToSuperview().offset(-13)
            make.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
        // 最后一项隐藏分割线
        if tag == DeleteAccountReason.allCases.count - 1 {
            separator.isHidden = true
        }
        
        // 添加点击事件
        button.addTarget(self, action: #selector(reasonOptionTapped(_:)), for: .touchUpInside)
        
        return button
    }
    
    // 设置协议内容
    private func setupAgreementContent() {
        let agreementItems = [
            "账号注销后，您将无法再使用该账号登录本平台。",
            "账号注销后，您的所有商家信息、商品数据、交易记录等将被永久删除且无法恢复。",
            "账号注销后，如有未完成的订单或交易，将按照平台规则处理。",
            "账号注销后，如有未结算的款项，将在结算完成后进行最终处理。",
            "账号注销操作不可撤销，请谨慎操作。",
            "账号注销前，请确认已处理完所有待办事项。"
        ]
        
        for item in agreementItems {
            let itemView = createAgreementItemView(content: item)
            agreementStackView.addArrangedSubview(itemView)
        }
        
        // 更新协议容器底部约束
        agreementContainerView.snp.makeConstraints { make in
            make.bottom.equalTo(agreementCheckContainer.snp.top).offset(-16)
        }
    }
    
    // 创建协议条款项视图
    private func createAgreementItemView(content: String) -> UIView {
        let containerView = UIView()
        
        let bulletPointView = UIView()
        bulletPointView.backgroundColor = UIColor(hex: "#999999")
        bulletPointView.layer.cornerRadius = 3
        
        let contentLabel = UILabel()
        contentLabel.text = content
        contentLabel.font = UIFont.systemFont(ofSize: 14)
        contentLabel.textColor = UIColor(hex: "#666666")
        contentLabel.numberOfLines = 0
        
        containerView.addSubview(bulletPointView)
        containerView.addSubview(contentLabel)
        
        bulletPointView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalToSuperview().offset(8)
            make.width.height.equalTo(6)
        }
        
        contentLabel.snp.makeConstraints { make in
            make.left.equalTo(bulletPointView.snp.right).offset(8)
            make.top.equalToSuperview()
            make.right.bottom.equalToSuperview()
        }
        
        return containerView
    }
    
    // 注销原因选项点击事件
    @objc private func reasonOptionTapped(_ sender: UIButton) {
        // 更新所有按钮的状态
        for button in reasonButtons {
            if let indicatorView = button.viewWithTag(100) as? UIImageView {
                if button.tag == sender.tag {
                    // 选中当前按钮
                    indicatorView.image = UIImage(systemName: "checkmark.circle.fill")
                    indicatorView.tintColor = UIColor(hex: "#FF9500")
                    selectedReason = DeleteAccountReason.allCases[button.tag]
                } else {
                    // 取消其他按钮的选中状态
                    indicatorView.image = UIImage(systemName: "circle")
                    indicatorView.tintColor = UIColor(hex: "#CCCCCC")
                }
            }
        }
        
        // 更新验证按钮状态
        updateVerifyButtonState()
        
        if DeleteAccountReason.allCases[sender.tag] == .other {
            // 展示文本输入框
            otherReasonContainerView.isHidden = false
            otherReasonHeightConstraint?.update(offset: 110) // 高度 110
            
            // 更新布局
            view.layoutIfNeeded()
            
            // 确保内容容器高度更新后，滚动视图能够显示所有内容
            DispatchQueue.main.async {
                self.updateScrollViewContentSize()
                
                // 滚动到其他原因输入框
                let rect = self.otherReasonContainerView.convert(self.otherReasonContainerView.bounds, to: self.scrollView)
                self.scrollView.scrollRectToVisible(rect, animated: true)
            }
        } else {
            otherReasonContainerView.isHidden = true
            otherReasonTextView.resignFirstResponder()
            otherReasonHeightConstraint?.update(offset: 0)
            
            // 更新布局
            view.layoutIfNeeded()
            
            // 更新滚动视图内容大小
            DispatchQueue.main.async {
                self.updateScrollViewContentSize()
            }
        }
    }
    
    // 更新验证按钮状态
    private func updateVerifyButtonState() {
        guard isAgreed else {
            updateVerifyButtonGradient(enabled: false)
            verifyButton.isEnabled = false
            return
        }

        if selectedReason == .other {
            let textValid = !otherReasonTextView.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            updateVerifyButtonGradient(enabled: textValid)
            verifyButton.isEnabled = textValid
            return
        }

        // 非 other
        updateVerifyButtonGradient(enabled: selectedReason != nil)
        verifyButton.isEnabled = selectedReason != nil
    }
    
    // 验证按钮点击事件
    @objc private func verifyButtonTapped() {
        guard let reason = selectedReason else {
            showToast("请选择注销原因")
            return
        }

        // 记录用户选择的注销原因
        print("用户选择的注销原因: \(reason.displayText)")

        // 显示系统弹窗
        let alertController = UIAlertController(
            title: "验证提示",
            message: "为确保账号安全，需要验证您的手机号。点击确定将跳转到验证手机号页面。",
            preferredStyle: .alert
        )

        // 添加取消按钮
        let cancelAction = UIAlertAction(title: "取消", style: .cancel)
        alertController.addAction(cancelAction)

        // 添加确定按钮
        let confirmAction = UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            self?.navigateToVerifyPhonePage()
        }
        alertController.addAction(confirmAction)

        // 显示弹窗
        present(alertController, animated: true)
    }

    // 跳转到验证手机号页面
    private func navigateToVerifyPhonePage() {
        let verifyVC = VerifyMobilePhoneNumberViewController()
        verifyVC.verificationType = .deleteAccount

        // 传递注销相关信息
        if let reason = selectedReason {
            // 根据枚举映射到对应的类型值
            switch reason {
            case .platformFeatures:
                verifyVC.deleteAccountType = 0
            case .costTooHigh:
                verifyVC.deleteAccountType = 1
            case .customerService:
                verifyVC.deleteAccountType = 2
            case .businessAdjustment:
                verifyVC.deleteAccountType = 3
            case .other:
                verifyVC.deleteAccountType = 4
            }

            verifyVC.deleteAccountReason = reason.displayText

            // 如果是其他原因，传递用户输入的详细说明
            if reason == .other {
                verifyVC.deleteAccountRemark = otherReasonTextView.text.trimmingCharacters(in: .whitespacesAndNewlines)
            } else {
                verifyVC.deleteAccountRemark = reason.displayText
            }
        }

        navigationController?.pushViewController(verifyVC, animated: true)
    }
    
    // MARK: - UITextViewDelegate
    func textViewDidChange(_ textView: UITextView) {
        let text = textView.text ?? ""
        // 限制 200 字符
        if text.count > 200 {
            textView.text = String(text.prefix(200))
        }
        charCountLabel.text = "\(textView.text.count)/200"
        placeholderLabel.isHidden = !textView.text.isEmpty
        updateVerifyButtonState()
    }
    
    // MARK: - 键盘处理
    @objc private func keyboardWillShow(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let keyboardFrame = userInfo[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }
        
        // 计算键盘高度相对于底部按钮的偏移
        let keyboardHeight = keyboardFrame.height
        // 考虑验证按钮的高度和间距
        let bottomInset = keyboardHeight
        
        // 设置滚动视图的内容内边距
        scrollView.contentInset.bottom = bottomInset
        scrollView.verticalScrollIndicatorInsets.bottom = bottomInset
        
        // 如果其他原因输入框可见，确保它不被键盘遮挡
        if !otherReasonContainerView.isHidden {
            // 将输入框转换为窗口坐标
            if let window = view.window {
                let otherReasonRect = otherReasonContainerView.convert(otherReasonContainerView.bounds, to: window)
                // 检查输入框是否被键盘遮挡
                let keyboardY = window.bounds.height - keyboardHeight
                if otherReasonRect.maxY > keyboardY {
                    // 计算需要滚动的偏移量
                    let offsetY = otherReasonRect.maxY - keyboardY + 20 // 额外的间距
                    let contentOffset = CGPoint(x: 0, y: scrollView.contentOffset.y + offsetY)
                    scrollView.setContentOffset(contentOffset, animated: true)
                }
            }
        }
        
        // 打印调试信息
        print("键盘显示 - 内容内边距: \(scrollView.contentInset.bottom)")
        print("键盘显示 - 内容大小: \(scrollView.contentSize)")
    }
    
    @objc private func keyboardWillHide(_ notification: Notification) {
        // 恢复到验证按钮的高度和间距
        scrollView.contentInset.bottom = 42 + 12 // 按钮高度 + 安全距离
        scrollView.verticalScrollIndicatorInsets.bottom = scrollView.contentInset.bottom
        
        // 打印调试信息
        print("键盘隐藏 - 内容内边距: \(scrollView.contentInset.bottom)")
    }
    
    // MARK: - 覆写 viewDidLayoutSubviews 更新渐变层 frame
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        verifyGradientLayer.frame = verifyButton.bounds
        
        // 使用辅助方法更新滚动视图内容大小
        DispatchQueue.main.async {
            self.updateScrollViewContentSize()
        }
    }
    
    // MARK: - 更新按钮渐变
    private func updateVerifyButtonGradient(enabled: Bool) {
        if enabled {
            verifyGradientLayer.colors = [UIColor(hex: "#FFBA20").cgColor, UIColor(hex: "#FF8827").cgColor]
        } else {
            let gray = UIColor(hex: "#CCCCCC").cgColor
            verifyGradientLayer.colors = [gray, gray]
        }
    }
    
    // 协议勾选事件
    @objc private func agreementCheckTapped() {
        isAgreed.toggle()
        
        // 更新图标
        if isAgreed {
            checkboxImageView.image = UIImage(named: "checkbox_checked")
        } else {
            checkboxImageView.image = UIImage(named: "checkbox_unchecked")
        }
        
        // 强制按钮立即更新布局
        agreementCheckContainer.layoutIfNeeded()
        
        // 打印调试信息
        print("协议勾选状态: \(isAgreed)")
        print("按钮图标位置: \(String(describing: checkboxImageView.frame))")
        print("按钮文字位置: \(String(describing: agreementLabel.frame))")
        
        updateVerifyButtonState()
    }
    
    // 在类的末尾添加一个辅助方法来更新滚动视图内容大小
    private func updateScrollViewContentSize() {
        // 使用实际布局后的高度
        let contentHeight = contentContainerView.frame.size.height
        let scrollViewHeight = scrollView.frame.height
        
        // 确保内容高度至少等于滚动视图高度
        let finalHeight = max(contentHeight, scrollViewHeight)
        
        // 设置内容大小
        scrollView.contentSize = CGSize(width: scrollView.frame.width, height: finalHeight)
        
        // 打印调试信息
        print("更新滚动视图内容大小: \(scrollView.contentSize)")
        print("内容容器实际高度: \(contentHeight)")
    }
}
