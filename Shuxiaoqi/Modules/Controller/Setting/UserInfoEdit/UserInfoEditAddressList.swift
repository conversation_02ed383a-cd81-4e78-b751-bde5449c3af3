//
//  UserInfoEditAddressList.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/5/21.
//
//  国际化地址选择器

import UIKit
import CoreLocation

// MARK: - 定位状态枚举
private enum LocationStatus {
    case loading
    case success(String)
    case failed
}

// MARK: - 自定义定位状态Cell
private class LocationStatusCell: UITableViewCell {
    let iconView = UIImageView()
    let mainLabel = UILabel()
    let bgContainer = UIView()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        setupUI()
    }
    
    required init?(coder: NSCoder) { super.init(coder: coder); setupUI() }
    
    private func setupUI() {
        bgContainer.backgroundColor = .white
        bgContainer.layer.cornerRadius = 10
        bgContainer.layer.masksToBounds = false
        bgContainer.layer.shadowColor = UIColor.black.withAlphaComponent(0.05).cgColor
        bgContainer.layer.shadowOpacity = 1
        bgContainer.layer.shadowOffset = CGSize(width: 0, height: 2)
        bgContainer.layer.shadowRadius = 6
        contentView.addSubview(bgContainer)
        bgContainer.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            bgContainer.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            bgContainer.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            bgContainer.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            bgContainer.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16)
        ])
        
        iconView.contentMode = .scaleAspectFit
        bgContainer.addSubview(iconView)
        iconView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            iconView.leadingAnchor.constraint(equalTo: bgContainer.leadingAnchor, constant: 12),
            iconView.centerYAnchor.constraint(equalTo: bgContainer.centerYAnchor),
            iconView.widthAnchor.constraint(equalToConstant: 20),
            iconView.heightAnchor.constraint(equalToConstant: 20)
        ])
        
        mainLabel.font = UIFont.systemFont(ofSize: 16)
        mainLabel.textColor = UIColor(hex: "#333333")
        bgContainer.addSubview(mainLabel)
        mainLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            mainLabel.leadingAnchor.constraint(equalTo: iconView.trailingAnchor, constant: 8),
            mainLabel.centerYAnchor.constraint(equalTo: bgContainer.centerYAnchor),
            mainLabel.trailingAnchor.constraint(equalTo: bgContainer.trailingAnchor, constant: -12)
        ])
    }
    
    func configure(status: LocationStatus) {
        switch status {
        case .loading:
            iconView.image = UIImage(named: "icon_gps")?.withTintColor(UIColor(hex: "#FF6236"), renderingMode: .alwaysOriginal)
            mainLabel.text = "正在获取当前位置..."
        case .success(let location):
            iconView.image = UIImage(named: "icon_gps")?.withTintColor(UIColor(hex: "#FF6236"), renderingMode: .alwaysOriginal)
            mainLabel.text = location
        case .failed:
            iconView.image = UIImage(named: "icon_incomplete_warning")?.withTintColor(UIColor(hex: "#FF6236"), renderingMode: .alwaysOriginal)
            mainLabel.text = "无法获取您的位置信息"
        }
    }
}

class UserInfoEditAddressList: BaseViewController, UITableViewDataSource, UITableViewDelegate, CLLocationManagerDelegate {

    private let tableView = UITableView(frame: .zero, style: .grouped)
    private let locationManager = CLLocationManager()
    private var locationStatus: CLAuthorizationStatus = .notDetermined
    private var currentLocationString: String = ""
    private var locationCellStatus: LocationStatus = .loading
    private var locationTimer: Timer?
    private var isLocationEnabled: Bool {
        return locationStatus == .authorizedWhenInUse || locationStatus == .authorizedAlways
    }

    private var countryList: [(code: String, name: String)] = []
    private var countryHasSubRegion: [String: Bool] = [:]
    private var districtDict: [String: Any] = [:]

    // 已选国家名称集合，由上级页面传入，用于高亮
    var selectedCountryNames: [String] = []

    // 选择完成回调，返回(id, name)
    var onAddressSelected: ((String, String) -> Void)?

    override func viewDidLoad() {
        super.viewDidLoad()
        navTitle = "选择你的地区"
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        showBackButton = false // 隐藏原有返回按钮
        setupCancelButton() // 添加自定义取消按钮
        setupTableView()
        loadCountryData() // 加载本地国家数据
        // 进入页面即定位
        locationCellStatus = .loading
        locationManager.delegate = self
        locationStatus = locationManager.authorizationStatus

        // 优化定位性能
        locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters // 降低精度要求，提高速度
        locationManager.requestWhenInUseAuthorization()
        locationManager.requestLocation()

        // 设置定位超时（10秒）
        locationTimer = Timer.scheduledTimer(withTimeInterval: 10.0, repeats: false) { [weak self] _ in
            guard let self = self else { return }
            if case .loading = self.locationCellStatus {
                self.locationCellStatus = .failed
                DispatchQueue.main.async {
                    self.tableView.reloadData()
                }
            }
        }

        tableView.reloadData()

        // 统一行高
        tableView.rowHeight = 46
    }

    deinit {
        // 清理定位超时定时器
        locationTimer?.invalidate()
        locationTimer = nil
    }

    private func setupTableView() {
        tableView.dataSource = self
        tableView.delegate = self
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "cell")
        tableView.register(LocationStatusCell.self, forCellReuseIdentifier: "locationStatusCell")
        tableView.separatorStyle = .none // 禁用系统分割线
        contentView.addSubview(tableView)
        tableView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 10),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor)
        ])
    }

    private func loadCountryData() {
        // 使用网络接口获取国家列表
        APIManager.shared.getRegionOption(params: [:]) { [weak self] result in
            guard let self = self else { return }
            switch result {
            case .success(let response):
                // 解析数据
                self.countryList = response.data.map { ($0.id, $0.name) }
                self.countryHasSubRegion = Dictionary(uniqueKeysWithValues: response.data.map { ($0.id, $0.isHasChildren) })
            case .failure(let error):
                print("❌ 获取国家列表失败: \(error)")
                self.countryList = []
            }
            DispatchQueue.main.async {
                self.tableView.reloadData()
            }
        }
    }

    private func setupCancelButton() {
        let cancelButton = UIButton(type: .system)
        cancelButton.setTitle("取消", for: .normal)
        cancelButton.setTitleColor(UIColor(hex: "#777777"), for: .normal)
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        cancelButton.contentHorizontalAlignment = .left
        cancelButton.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        // 直接访问 navBar
        let navBar = self.navBar
        navBar.addSubview(cancelButton)
        cancelButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            cancelButton.leadingAnchor.constraint(equalTo: navBar.leadingAnchor, constant: 12),
            cancelButton.centerYAnchor.constraint(equalTo: navBar.centerYAnchor),
            cancelButton.widthAnchor.constraint(greaterThanOrEqualToConstant: 40),
            cancelButton.heightAnchor.constraint(equalToConstant: 44)
        ])
    }

    @objc private func cancelButtonTapped() {
        // 关闭当前页面
        if let navigationController = navigationController, navigationController.viewControllers.count > 1 {
            navigationController.popViewController(animated: true)
        } else {
            dismiss(animated: true, completion: nil)
        }
    }

    // MARK: - TableView DataSource

    func numberOfSections(in tableView: UITableView) -> Int { 2 }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if section == 0 { return 1 }
        return countryList.count
    }   

    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        if section == 1 { return "全部" }
        return nil
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if indexPath.section == 0 {
            let cell = tableView.dequeueReusableCell(withIdentifier: "locationStatusCell", for: indexPath) as! LocationStatusCell
            cell.configure(status: locationCellStatus)
            return cell
        } else {
            let cell = tableView.dequeueReusableCell(withIdentifier: "cell", for: indexPath)
            // 清除旧视图
            cell.contentView.subviews.forEach { $0.removeFromSuperview() }

            let (code, name) = countryList[indexPath.row]

            // 背景容器
            let bgView = UIView()
            bgView.backgroundColor = .white
            bgView.layer.masksToBounds = true

            // 根据位置设置圆角
            if indexPath.row == 0 && indexPath.row == countryList.count - 1 {
                // 仅一行
                bgView.layer.cornerRadius = 5
            } else if indexPath.row == 0 {
                bgView.layer.cornerRadius = 5
                bgView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
            } else if indexPath.row == countryList.count - 1 {
                bgView.layer.cornerRadius = 5
                bgView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
            } else {
                bgView.layer.cornerRadius = 0 // 中间行无圆角
            }

            cell.contentView.addSubview(bgView)
            bgView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                bgView.leadingAnchor.constraint(equalTo: cell.contentView.leadingAnchor, constant: 19),
                bgView.trailingAnchor.constraint(equalTo: cell.contentView.trailingAnchor, constant: -19),
                bgView.topAnchor.constraint(equalTo: cell.contentView.topAnchor),
                bgView.bottomAnchor.constraint(equalTo: cell.contentView.bottomAnchor)
            ])

            // 标题
            let titleLabel = UILabel()
            titleLabel.font = UIFont.systemFont(ofSize: 16)
            titleLabel.textColor = UIColor(hex: "#333333")
            titleLabel.text = name
            bgView.addSubview(titleLabel)
            titleLabel.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                titleLabel.leadingAnchor.constraint(equalTo: bgView.leadingAnchor, constant: 15),
                titleLabel.centerYAnchor.constraint(equalTo: bgView.centerYAnchor)
            ])

            // 右侧箭头
            if countryHasSubRegion[code] == true {
                cell.accessoryType = .none // 先清除默认
                let arrow = UIImageView(image: UIImage(systemName: "chevron.right")?.withTintColor(UIColor(hex: "#C7C7CC"), renderingMode: .alwaysOriginal))
                arrow.contentMode = .scaleAspectFit
                bgView.addSubview(arrow)
                arrow.translatesAutoresizingMaskIntoConstraints = false
                NSLayoutConstraint.activate([
                    arrow.trailingAnchor.constraint(equalTo: bgView.trailingAnchor, constant: -12),
                    arrow.centerYAnchor.constraint(equalTo: bgView.centerYAnchor),
                    arrow.widthAnchor.constraint(equalToConstant: 16),
                    arrow.heightAnchor.constraint(equalToConstant: 16)
                ])
            }

            // 已选地区标签
            if selectedCountryNames.contains(name) {
                let selectedLabel = UILabel()
                selectedLabel.text = "已选地区"
                selectedLabel.font = UIFont.systemFont(ofSize: 13)
                selectedLabel.textColor = UIColor(hex: "#333333")
                bgView.addSubview(selectedLabel)
                selectedLabel.translatesAutoresizingMaskIntoConstraints = false
                NSLayoutConstraint.activate([
                    selectedLabel.trailingAnchor.constraint(equalTo: bgView.trailingAnchor, constant: -38),
                    selectedLabel.centerYAnchor.constraint(equalTo: bgView.centerYAnchor)
                ])
            }

            // 自定义分割线（非最后一行）
            if indexPath.row < countryList.count - 1 {
                let line = UIView()
                line.backgroundColor = UIColor(hex: "#E5E5E5")
                bgView.addSubview(line)
                line.translatesAutoresizingMaskIntoConstraints = false
                NSLayoutConstraint.activate([
                    line.leadingAnchor.constraint(equalTo: bgView.leadingAnchor, constant: 16),
                    line.trailingAnchor.constraint(equalTo: bgView.trailingAnchor, constant: -16),
                    line.bottomAnchor.constraint(equalTo: bgView.bottomAnchor),
                    line.heightAnchor.constraint(equalToConstant: 0.5)
                ])
            }

            cell.backgroundColor = UIColor.clear
            return cell
        }
    }

    // MARK: - TableView Delegate

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        if indexPath.section == 0 {
            if case .failed = locationCellStatus {
                // 跳转到系统设置
                if let url = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(url)
                }
            } else if case .success(let location) = locationCellStatus {
                print("选中当前位置：\(location)")
                // 为定位获取的地区提供特殊标识ID
                self.onAddressSelected?("location_based", location)
                navigationController?.popViewController(animated: true)
            } else {
                // 正在定位中，什么都不做
            }
        } else {
            let (code, name) = countryList[indexPath.row]
            if countryHasSubRegion[code] == true {
                let subVC = UserInfoEditAddressSubListViewController()
                subVC.countryCode = code
                subVC.countryName = name
                
                // 传递已选地区名称，保持服务器存储的值不变
                // 如果服务器存储的第一级是当前选中的国家，则传递剩余部分作为已选省份
                if !selectedCountryNames.isEmpty && selectedCountryNames[0] == name {
                    subVC.selectedProvinceNames = selectedCountryNames.count > 1 ? Array(selectedCountryNames.dropFirst()) : []
                } else {
                    // 选中了不同国家，不传递任何已选省份
                    subVC.selectedProvinceNames = []
                }
                
                // 设置回调
                subVC.onAddressSelected = { [weak self] selectedId, fullAddress in
                    guard let self = self else { return }
                    self.onAddressSelected?(selectedId, fullAddress)
                }
                
                navigationController?.pushViewController(subVC, animated: true)
            } else {
                print("选中 \(name)")
                self.onAddressSelected?(code, name)
                navigationController?.popViewController(animated: true)
            }
        }
    }

    // MARK: - 定位代理

    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        locationStatus = status
        if isLocationEnabled {
            locationManager.requestLocation()
        } else {
            locationCellStatus = .failed
            tableView.reloadData()
        }
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        // 清除定位超时定时器
        locationTimer?.invalidate()
        locationTimer = nil

        guard let location = locations.first else {
            locationCellStatus = .failed
            tableView.reloadData()
            return
        }
        // 反向地理编码
        let geocoder = CLGeocoder()
        geocoder.reverseGeocodeLocation(location) { [weak self] placemarks, error in
            guard let self = self else { return }
            if let placemark = placemarks?.first {
                let country = placemark.country ?? ""
                let province = placemark.administrativeArea ?? ""
                let city = placemark.locality ?? ""
                let locationString = [country, province, city].filter { !$0.isEmpty }.joined(separator: " ")
                self.locationCellStatus = .success(locationString)
            } else {
                self.locationCellStatus = .failed
            }
            DispatchQueue.main.async { self.tableView.reloadData() }
        }
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        // 清除定位超时定时器
        locationTimer?.invalidate()
        locationTimer = nil

        locationCellStatus = .failed
        tableView.reloadData()
    }

    // MARK: - Header 样式
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 13)
        label.textColor = UIColor(hex: "#999999")
        label.backgroundColor = UIColor.clear
        if section == 0 {
            label.text = "    定位到的位置" // 前置留 4pt
        } else if section == 1 {
            label.text = "    全部"
        }
        return label
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 32
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        // Section 0: 定位卡片高度根据权限状态变化
        if indexPath.section == 0 {
            // 计算设计稿高度：导航栏下到"全部"标题需 114 或 137
            // 已知：顶部 tableView 顶距 10，section header 32
            // => 114 - 42 = 72  (正常)
            // => 137 - 42 = 95  (无权限 + 提示)
//            switch locationCellStatus {
            // case .failed:
            //     return 95
            // default:
            //     return 62
            //
                return 62
//            }
        }
        // 其他行统一 46
        return 46
    }

    // 新增footer view
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        if section == 0, case .failed = locationCellStatus {
            let label = UILabel()
            label.font = UIFont.systemFont(ofSize: 13)
            label.textColor = UIColor(hex: "#999999")
            label.numberOfLines = 0
            label.text = "定位失败，您可以在设置 -> 隐私 -> 定位服务中开启定位"
            label.backgroundColor = .clear
            label.textAlignment = .left
            let container = UIView()
            container.backgroundColor = .clear
            container.addSubview(label)
            label.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                label.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 32),
                label.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -32),
                label.topAnchor.constraint(equalTo: container.topAnchor, constant: 0),
                label.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: 0)
            ])
            return container
        }
        return nil
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        if section == 0, case .failed = locationCellStatus {
            return 33
        }
        return 0.01
    }
}

