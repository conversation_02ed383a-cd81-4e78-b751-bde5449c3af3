//
//  PhoneBindingViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/3/20.
//

import UIKit
import SnapKit

class PhoneBindingViewController: BaseViewController {
    
    // MARK: - UI 组件
    var bindPhoneId = ""
    
    var checkId = ""
    
    // 新增: 用于存储图形验证码请求的 ID
    private var codeId: String = ""
    
    // 手机号输入框容器
    private lazy var phoneInputContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 手机号输入框
    private lazy var phoneTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "请输入新绑定的手机号"
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.backgroundColor = .white
        textField.textColor = UIColor(hex: "#000000", alpha: 0.45)
        textField.keyboardType = .numberPad
        return textField
    }()
    
    // 手机号下划线
    private lazy var phoneUnderline: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#000000", alpha: 0.1)
        return view
    }()
    
    // 验证码输入框容器
    private lazy var verificationCodeContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 验证码输入框
    private lazy var verificationCodeTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "请输入图形验证码"
        textField.backgroundColor = .white
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = UIColor(hex: "#333333")
        return textField
    }()
    
    // 验证码下划线
    private lazy var verificationUnderline: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#000000", alpha: 0.1)
        return view
    }()
    
    // 图形验证码图片
    private lazy var captchaImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.backgroundColor = UIColor(hex: "#EEEEEE")
        imageView.clipsToBounds = true
        imageView.isUserInteractionEnabled = true
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(refreshCaptcha))
        imageView.addGestureRecognizer(tapGesture)
        
        return imageView
    }()
    
    // 短信验证码输入框容器
    private lazy var smsCodeContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    // 短信验证码输入框
    private lazy var smsCodeTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "请输入验证码"
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.backgroundColor = .white
        textField.textColor = UIColor(hex: "#333333")
        textField.keyboardType = .numberPad
        return textField
    }()
    
    // 短信验证码下划线
    private lazy var smsUnderline: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#000000", alpha: 0.1)
        return view
    }()
    
    // 获取验证码按钮
    private lazy var getSmsCodeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("获取验证码", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor(hex: "#FFA245")
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.layer.cornerRadius = 16 // 32/2 = 16，实现圆角46的效果
        button.addTarget(self, action: #selector(getSmsCodeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 协议选择按钮
    private lazy var agreementCheckbox: UIButton = {
        let button = UIButton(type: .custom)
        let defaultImage = UIImage(named: "app_radio_Default")?.withRenderingMode(.alwaysOriginal)
        let selectedImage = UIImage(named: "app_radio_select")?.withRenderingMode(.alwaysOriginal)
        button.setImage(defaultImage, for: .normal)
        button.setImage(selectedImage, for: .selected)
        button.addTarget(self, action: #selector(agreementCheckboxTapped), for: .touchUpInside)
        return button
    }()
    
    // 用户协议相关
    private lazy var agreementLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textAlignment = .left
        
        // 创建属性字符串
        let attributedString = NSMutableAttributedString(string: "绑定即代表同意")
        attributedString.addAttribute(.foregroundColor, value: UIColor(hex: "#999999"), range: NSRange(location: 0, length: attributedString.length))
        
        // 添加协议文本
        let agreementText = NSAttributedString(string: "《用户协议》", attributes: [
            .foregroundColor: UIColor(hex: "#0256FF")
        ])
        attributedString.append(agreementText)
        
        // 添加"和"
        let andText = NSAttributedString(string: "和", attributes: [
            .foregroundColor: UIColor(hex: "#999999")
        ])
        attributedString.append(andText)
        
        // 添加隐私政策文本
        let privacyText = NSAttributedString(string: "《隐私政策》", attributes: [
            .foregroundColor: UIColor(hex: "#0256FF")
        ])
        attributedString.append(privacyText)
        
        label.attributedText = attributedString
        label.isUserInteractionEnabled = true
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(agreementTapped(_:)))
        label.addGestureRecognizer(tapGesture)
        
        return label
    }()
    
    // 立即绑定按钮
    private lazy var bindButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("立即绑定", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = UIColor(hex: "#FF8F1F")
        button.layer.cornerRadius = 6
        button.addTarget(self, action: #selector(bindButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 新增：首次登录标记
    var isFirstTimeLogin: Bool = false
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        self.contentView.backgroundColor = .white
        
        // 设置导航栏标题
        navTitle = "绑定手机号"
        
        setupUI()
        
        // 添加点击背景关闭键盘的手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        tapGesture.cancelsTouchesInView = false
        view.addGestureRecognizer(tapGesture)
        
        // 加载初始验证码
        refreshCaptcha()
    }
    
    // MARK: - UI 设置
    
    private func setupUI() {
        // 添加手机号输入框
        contentView.addSubview(phoneInputContainer)
        phoneInputContainer.snp.makeConstraints { make in
            make.top.equalTo(navBar.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(60)
        }
        
        phoneInputContainer.addSubview(phoneTextField)
        phoneTextField.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        
        phoneInputContainer.addSubview(phoneUnderline)
        phoneUnderline.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
        
        // 添加验证码输入框
        contentView.addSubview(verificationCodeContainer)
        verificationCodeContainer.snp.makeConstraints { make in
            make.top.equalTo(phoneInputContainer.snp.bottom).offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(60)
        }
        
        verificationCodeContainer.addSubview(verificationCodeTextField)
        verificationCodeTextField.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-122) // 90 + 32
            make.centerY.equalToSuperview()
        }
        
        verificationCodeContainer.addSubview(captchaImageView)
        captchaImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(90)
            make.height.equalTo(30)
        }
        
        verificationCodeContainer.addSubview(verificationUnderline)
        verificationUnderline.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
        
        // 添加短信验证码输入框
        contentView.addSubview(smsCodeContainer)
        smsCodeContainer.snp.makeConstraints { make in
            make.top.equalTo(verificationCodeContainer.snp.bottom).offset(16)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(60)
        }
        
        smsCodeContainer.addSubview(smsCodeTextField)
        smsCodeTextField.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-100)
            make.centerY.equalToSuperview()
        }
        
        smsCodeContainer.addSubview(getSmsCodeButton)
        getSmsCodeButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(90)
            make.height.equalTo(32)
        }
        
        smsCodeContainer.addSubview(smsUnderline)
        smsUnderline.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(1)
        }
        
        // 添加绑定按钮和协议到contentView
        contentView.addSubview(bindButton)
        
        // 创建协议容器视图
        let agreementContainer = UIView()
        contentView.addSubview(agreementContainer)
        contentView.addSubview(agreementCheckbox)
        contentView.addSubview(agreementLabel)
        
        // 先设置绑定按钮的约束
        bindButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalTo(contentView.safeAreaLayoutGuide).offset(-8)
            make.height.equalTo(43)
        }
        
        // 设置协议容器视图约束
        agreementContainer.snp.makeConstraints { make in
            make.bottom.equalTo(bindButton.snp.top).offset(-8)
            make.centerX.equalToSuperview()
            make.height.equalTo(20)
        }
        
        // 然后设置协议相关的约束
        agreementCheckbox.snp.makeConstraints { make in
            make.centerY.equalTo(agreementContainer)
            make.width.height.equalTo(14)
            make.left.equalTo(agreementContainer)
        }
        
        agreementLabel.snp.makeConstraints { make in
            make.centerY.equalTo(agreementContainer)
            make.left.equalTo(agreementCheckbox.snp.right).offset(9)
            make.right.equalTo(agreementContainer)
        }
    }
    
    // MARK: - 事件响应方法
    
    @objc private func refreshCaptcha() {
        print("PhoneBinding: 刷新图形验证码")
        captchaImageView.image = nil // 清空旧图片
        captchaImageView.subviews.forEach { $0.removeFromSuperview() } // 清空子视图（如图标或指示器）

        // 显示加载指示器
        let activityIndicator = UIActivityIndicatorView(style: .medium)
        activityIndicator.translatesAutoresizingMaskIntoConstraints = false
        captchaImageView.addSubview(activityIndicator)
        NSLayoutConstraint.activate([
            activityIndicator.centerXAnchor.constraint(equalTo: captchaImageView.centerXAnchor),
            activityIndicator.centerYAnchor.constraint(equalTo: captchaImageView.centerYAnchor)
        ])
        activityIndicator.startAnimating()

        // 生成新的 codeId
        let timestamp = Int(Date().timeIntervalSince1970 * 1000)
        let random1 = Int.random(in: 0...9)
        let random2 = Int.random(in: 0...9)
        self.codeId = "\(timestamp)\(random1)\(random2)"
        print("PhoneBinding: Generated new codeId: \(self.codeId)")

        // 调用 API 获取图形验证码，type 为 "bindPhone"
        APIManager.shared.getCaptcha(codeId: self.codeId, type: "bindPhone") { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                activityIndicator.removeFromSuperview()
                switch result {
                case .success(let image):
                    if let img = image {
                        self.captchaImageView.image = img
                        print("PhoneBinding: Captcha image loaded successfully.")
                    } else {
                        self.showToast("无法显示验证码图片")
                        self.captchaImageView.image = UIImage(systemName: "exclamationmark.triangle")
                        print("PhoneBinding: Failed to decode captcha image.")
                    }
                case .failure(let error):
                    self.showToast(error.errorMessage)
                    self.captchaImageView.image = UIImage(systemName: "exclamationmark.triangle")
                    print("PhoneBinding: Failed to fetch captcha: \(error.errorMessage)")
                }
            }
        }
    }
    
    @objc private func getSmsCodeButtonTapped() {
        print("PhoneBinding: 获取短信验证码按钮点击")
        
        guard let phone = phoneTextField.text, phone.count == 11 else {
            showToast("请输入正确的手机号码")
            return
        }
        
        // 取消注释：需要图形验证码
        guard let captchaCode = verificationCodeTextField.text, !captchaCode.isEmpty else {
            showToast("请输入图形验证码")
            return
        }
        
        // **修改点**: 检查 codeId 是否已生成
        guard !self.codeId.isEmpty else {
            showToast("请先刷新图形验证码") // 或者自动刷新？
            print("PhoneBinding: codeId is empty, cannot request SMS code.")
            return
        }

        // 添加加载状态或禁用按钮 (可选)
        // showLoading()
        // getSmsCodeButton.isEnabled = false // 可以在 startCountdown 中处理

        print("PhoneBinding: Requesting SMS code with phone: \(phone), captcha: \(captchaCode), codeId: \(self.codeId), type: bindPhone")

        // 调用真实的 API
        APIManager.shared.getSMSCode(phone: phone, captchaCode: captchaCode, codeId: self.codeId, type: "bindPhone") { [weak self] result in
            guard let self = self else { return }
            // hideLoading() // 隐藏加载状态

            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    // 假设 SMSCodeResponse 结构定义了 isSuccess 和 displayMessage
                    if response.isSuccess {
                        self.startCountdown() // 仅在成功发送时启动倒计时
                        self.showToast("验证码已发送")
                        print("PhoneBinding: SMS code sent successfully.")
                    } else {
                        // API 调用成功，但业务逻辑失败 (例如图形验证码错误)
                        self.showToast(response.displayMessage)
                        print("PhoneBinding: Failed to send SMS code: \(response.displayMessage)")
                        // 如果是图形验证码相关错误，刷新它
                        if response.displayMessage.contains("图形验证码") || response.displayMessage.contains("captcha") { // 关键词匹配可能需要调整
                            self.refreshCaptcha()
                            self.verificationCodeTextField.text = "" // 清空输入框
                        }
                        // 重新启用按钮，允许重试 (如果倒计时未启动)
                        // if !getSmsCodeButton.isEnabled { getSmsCodeButton.isEnabled = true } // 或者在 startCountdown 开始时处理
                    }
                case .failure(let error):
                    // 网络或解析错误
                    self.showToast(error.errorMessage)
                    print("PhoneBinding: Network or parsing error fetching SMS code: \(error.errorMessage)")
                    // 重新启用按钮，允许重试
                    // if !getSmsCodeButton.isEnabled { getSmsCodeButton.isEnabled = true }
                }
            }
        }

        // --- 移除之前的模拟成功代码 ---
        // startCountdown()
        // showToast(message: "(模拟)验证码已发送")
    }
    
    @objc private func bindButtonTapped() {
        // 1. 验证是否同意协议
        guard agreementCheckbox.isSelected else {
            showToast("请先同意用户协议和隐私政策")
            return
        }

        // 2. 验证手机号输入
        guard let phone = phoneTextField.text, phone.count == 11 else {
            showToast("请输入正确的手机号码")
            return
        }

        // 3. 验证图形验证码输入 (虽然绑定接口可能不直接用，但流程上用户需要填写)
        guard let captchaCode = verificationCodeTextField.text, !captchaCode.isEmpty else {
            showToast("请输入图形验证码")
            return
        }

        // 4. 验证短信验证码输入
        guard let smsCode = smsCodeTextField.text, !smsCode.isEmpty else {
            showToast("请输入短信验证码")
            return
        }

        // 5. 根据 ID 调用对应的 API
        // 添加加载指示器 (如果需要)
        // showLoading()

        if !bindPhoneId.isEmpty {
            // 调用微信登录绑定手机号接口
            print("PhoneBinding: Calling bingUserPhone API...")
            APIManager.shared.bingUserPhone(bingPhoneId: self.bindPhoneId, code: smsCode, phone: phone) { [weak self] result in
                guard let self = self else { return }
                // hideLoading() // 隐藏加载

                DispatchQueue.main.async {
                    switch result {
                    case .success(let response):
                        // 两个接口都返回 LoginResponse
                        if response.isSuccess {
                            print("PhoneBinding: bingUserPhone successful.")
                            // 绑定成功逻辑 (微信首次登录)
                            // 1. 保存 Token (假设有 UserManager)
                            if let token = response.data?.tokenValue {
                                print("Saving token from bingUserPhone: \(token)")
                                // UserManager.shared.saveToken(token) // 替换为你的 Token 保存逻辑
                            }
                            // 2. Dismiss 当前页面
                            self.showToast("绑定成功") // 显示一下提示
                            // 延迟 dismiss 让用户看到 Toast
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                // 发送首次登录完成通知（仅首次登录时）
                                if self.isFirstTimeLogin {
                                    NotificationCenter.default.post(name: .firstTimeLoginCompleted, object: nil)
                                }
                                self.dismiss(animated: true, completion: nil)
                            }
                        } else {
                            print("PhoneBinding: bingUserPhone failed: \(response.displayMessage)")
                            self.showToast("\(response.displayMessage)")
                            // 验证码错误等情况，可能需要刷新图形码
                            if response.displayMessage.contains("验证码") {
                                self.refreshCaptcha()
                            }
                        }
                    case .failure(let error):
                         print("PhoneBinding: bingUserPhone network error: \(error.errorMessage)")
                        self.showToast("绑定出错: \(error.errorMessage)")
                    }
                }
            }
        } else if !checkId.isEmpty {
            // 调用更换新手机号接口
            print("PhoneBinding: Calling bangPhone API...")
            APIManager.shared.bangPhone(phone: phone, code: smsCode, checkId: self.checkId) { [weak self] result in
                guard let self = self else { return }
                 // hideLoading() // 隐藏加载

                DispatchQueue.main.async {
                    switch result {
                    case .success(let response):
                        // 两个接口都返回 LoginResponse
                        if response.isSuccess {
                            print("PhoneBinding: bangPhone successful.")
                            // 更换成功逻辑 (从设置进入)
                            // 1. 保存 Token (假设有 UserManager)
                            if let token = response.data?.tokenValue {
                                print("Saving token from bangPhone: \(token)")
                                // UserManager.shared.saveToken(token) // 替换为你的 Token 保存逻辑
                            }
                            // 2. 显示成功 Toast
                            self.showToast("手机号更换成功")
                            // 3. Pop 回到设置页面 (上上个页面)
                            guard let navigationController = self.navigationController else { return }
                            let viewControllers = navigationController.viewControllers
                            // 至少需要3个 VC (设置页 -> 验证旧手机页 -> 绑定新手机页) 才能 pop 回去
                            if viewControllers.count >= 3 {
                                let targetViewController = viewControllers[viewControllers.count - 3]

                                // 刷新验证页面的脱敏手机号
                                if let verifyVC = viewControllers[viewControllers.count - 2] as? VerifyMobilePhoneNumberViewController {
                                    verifyVC.refreshDesensitizedPhoneNumber()
                                }

                                // 延迟 pop 让用户看到 Toast
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    navigationController.popToViewController(targetViewController, animated: true)
                                }
                            } else {
                                // 如果层级不够，直接返回上一页或根页面作为备选
                                 DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                     if navigationController.viewControllers.count > 1 {
                                         navigationController.popViewController(animated: true)
                                     } else {
                                         // 或者处理其他逻辑，例如 popToRoot
                                         navigationController.popToRootViewController(animated: true)
                                     }
                                 }
                            }
                        } else {
                             print("PhoneBinding: bangPhone failed: \(response.displayMessage)")
                            self.showToast("\(response.displayMessage)")
                             // 验证码错误等情况，可能需要刷新图形码
                            if response.displayMessage.contains("验证码") {
                                self.refreshCaptcha()
                            }
                        }
                    case .failure(let error):
                         print("PhoneBinding: bangPhone network error: \(error.errorMessage)")
                        self.showToast("\(error.errorMessage)")
                    }
                }
            }
        } else {
            // 理论上不应发生，因为页面进入时应有其中一个 ID
            // hideLoading() // 隐藏加载
            print("PhoneBinding Error: Neither bindPhoneId nor checkId is set.")
            showToast("发生未知错误，请返回重试")
        }
    }
    
    @objc private func agreementTapped(_ gesture: UITapGestureRecognizer) {
        let point = gesture.location(in: agreementLabel)
        let attributedText = agreementLabel.attributedText as NSAttributedString?
        let textStorage = NSTextStorage(attributedString: attributedText!)
        let layoutManager = NSLayoutManager()
        textStorage.addLayoutManager(layoutManager)
        let textContainer = NSTextContainer(size: agreementLabel.bounds.size)
        layoutManager.addTextContainer(textContainer)
        
        let characterIndex = layoutManager.characterIndex(for: point, in: textContainer, fractionOfDistanceBetweenInsertionPoints: nil)
        
        if characterIndex < textStorage.length {
            let range1 = (attributedText!.string as NSString).range(of: "《用户协议》")
            let range2 = (attributedText!.string as NSString).range(of: "《隐私政策》")
            
            if NSLocationInRange(characterIndex, range1) {
                print("点击了用户协议")
                // 跳转到用户协议页面
            } else if NSLocationInRange(characterIndex, range2) {
                print("点击了隐私政策")
                // 跳转到隐私政策页面
            }
        }
    }
    
    @objc private func agreementCheckboxTapped() {
        agreementCheckbox.isSelected.toggle()
    }
    
    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }
    
    // MARK: - 辅助方法
    
    private func startCountdown() {
        // 实现倒计时逻辑
        var seconds = 60
        getSmsCodeButton.isEnabled = false
        getSmsCodeButton.setTitle("\(seconds)秒后重试", for: .disabled)
        getSmsCodeButton.setTitleColor(.white, for: .disabled)
        getSmsCodeButton.backgroundColor = UIColor(hex: "#CCCCCC") // 添加灰色背景
        
        // 创建定时器
        let timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            seconds -= 1
            self?.getSmsCodeButton.setTitle("\(seconds)秒后重试", for: .disabled)
            
            if seconds <= 0 {
                timer.invalidate()
                self?.getSmsCodeButton.isEnabled = true
                self?.getSmsCodeButton.setTitle("获取验证码", for: .normal)
                self?.getSmsCodeButton.backgroundColor = UIColor(hex: "#FFA245") // 恢复原始橙色背景
            }
        }
        
        // 将定时器添加到当前运行循环
        RunLoop.current.add(timer, forMode: .common)
    }
    
//    private func showToast(message: String) {
//        // 简单的Toast提示实现
//        let toastLabel = UILabel()
//        toastLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
//        toastLabel.textColor = .white
//        toastLabel.textAlignment = .center
//        toastLabel.font = UIFont.systemFont(ofSize: 14)
//        toastLabel.text = message
//        toastLabel.alpha = 1.0
//        toastLabel.layer.cornerRadius = 10
//        toastLabel.clipsToBounds = true
//        toastLabel.numberOfLines = 0
//        
//        // 设置文本内边距
//        let padding = UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16)
//        toastLabel.frame = CGRect(x: 0, y: 0, width: 200, height: 44) // 临时frame用于文本计算
//        
//        // 更新frame计算文本高度
//        let textWidth = UIScreen.main.bounds.width - 80 - padding.left - padding.right
//        let size = CGSize(width: textWidth, height: .greatestFiniteMagnitude)
//        let textRect = message.boundingRect(
//            with: size,
//            options: .usesLineFragmentOrigin,
//            attributes: [NSAttributedString.Key.font: toastLabel.font!],
//            context: nil
//        )
//        
//        // 创建包含Toast的容器视图以便应用padding
//        let containerView = UIView()
//        containerView.backgroundColor = UIColor.black.withAlphaComponent(0.7)
//        containerView.layer.cornerRadius = 10
//        containerView.clipsToBounds = true
//        
//        view.addSubview(containerView)
//        containerView.addSubview(toastLabel)
//        
//        // 设置容器约束
//        containerView.snp.makeConstraints { make in
//            make.center.equalToSuperview()
//            make.width.lessThanOrEqualToSuperview().offset(-80) // 比之前更大的边距
//            make.height.greaterThanOrEqualTo(44)
//        }
//        
//        // 设置文本标签约束包含内边距
//        toastLabel.snp.makeConstraints { make in
//            make.edges.equalToSuperview().inset(padding)
//        }
//        
//        UIView.animate(withDuration: 0.5, delay: 1.5, options: .curveEaseOut, animations: {
//            containerView.alpha = 0.0
//        }, completion: { _ in
//            containerView.removeFromSuperview()
//        })
//    }
    
    // 页面关闭时也发送通知（如用户手动关闭）
    deinit {
        if isFirstTimeLogin {
            NotificationCenter.default.post(name: .firstTimeLoginCompleted, object: nil)
        }
    }
}

