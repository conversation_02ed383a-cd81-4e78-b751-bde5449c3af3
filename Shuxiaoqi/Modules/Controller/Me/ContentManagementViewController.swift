//
//  ContentManagementViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/21.
//

//  内容管理
import UIKit
import MJRefresh
import Kingfisher

class ContentManagementViewController: BaseViewController {
    
    // MARK: - 内部类型定义
    
    // 发布状态枚举
    enum ContentManagementPublishStatus {
        case published
        case unpublished
        case reviewing
        case reviewFailed  // 添加审核失败状态
    }
    
    // 内容项模型
    struct ContentManagementItem {
        var id: String
        var title: String
        var tags: [String]
        var viewCount: Int
        var likeCount: Int
        var isSelected: Bool = false
        var publishStatus: ContentManagementPublishStatus
        var coverImageUrl: String // 添加封面图片URL
        var updateTime: String // 新增发布时间字段
        var refuseReason: String? // 新增审核失败原因
    }
    
    // MARK: - 属性
    
    // 分页相关属性
    private var currentPage: Int = 0
    private var pageSize: Int = 10
    private var hasMoreData: Bool = true
    private var isLoading: Bool = false
    
    // 当前内容筛选状态
    private var currentContentStatus: Int = 0 // 0: 全部内容, 1: 已发布, 2: 已下架, 3: 审核中
    
    // 搜索关键词
    private var currentSearchKeyword: String = ""
    
    // 是否处于批量管理模式
    private var isBatchManageMode: Bool = false
    
    // 是否全选
    private var isSelectAll: Bool = false
    
    // 搜索框
    private var searchBar: UITextField!
    
    // 搜索框容器
    private var searchContainer: UIView!
    
    // 分段控制器容器
    private var segmentContainer: UIView!
    
    // 分段控制器选项
    private var segmentOptions: [UIButton] = []
    
    // 分段控制器下划线
    private var segmentIndicator: UIView!
    
    // 当前选中的分段索引
    private var selectedSegmentIndex: Int = 0
    
    // 全选按钮
    private var selectAllButton: UIButton!
    
    // 批量下架按钮
    private var batchRemoveButton: UIButton!
    
    // 批量删除按钮
    private var batchDeleteButton: UIButton!
    
    // 操作按钮容器
    private var operationContainer: UIView!
    
    // 表格视图
    private var tableView: UITableView!
    
    // 右上角批量管理按钮
    private var batchManageButton: UIButton!
    
    // 内容项数组
    var contentItems: [ContentManagementItem] = []
    
    // 发布按钮
    private var publishButton: UIButton!
    
    // 筛选条件
    private var currentSort: Int?
    private var currentWorksCategoryId: String?
    private var currentTime: Int?
    private var currentDuration: Int?
    
    // 临时筛选条件（筛选菜单中使用）
    private var tempSort: Int?
    private var tempWorksCategoryId: String?
    private var tempTime: Int?
    private var tempDuration: Int?
    
    // 视频类型数据（第一个元素为"全部"，总数最多9）
    private var videoTypeTitles: [String] = ["全部"]
    private var videoTypeIds: [String] = [""]
    
    // MARK: - 视频分类获取
    private func fetchVideoTypeList() {
        APIManager.shared.getVideoTypeList(isHomeShow: false) { [weak self] result in
            guard let self = self else { return }
            switch result {
            case .success(let response):
                guard response.isSuccess, let data = response.data else { return }
                var titles: [String] = ["全部"]
                var ids: [String] = [""]
                for item in data.prefix(8) { // 限制最多显示8个(加上"全部"共9个)
                    if let name = item.typeName, let id = item.id {
                        titles.append(name)
                        ids.append(String(id))
                    }
                }
                self.videoTypeTitles = titles
                self.videoTypeIds = ids
            case .failure(let error):
                print("获取视频分类失败: \(error.localizedDescription)")
            }
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置导航栏标题
        navTitle = "内容管理"
        navBar.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 确保使用默认导航栏
        useCustomNavView = false
        showNavBar = true
        
        // 设置右上角批量管理按钮
        setupRightNavButton()
        
        // 设置UI
        setupUI()
        
        // 显示系统导航栏
        navigationController?.setNavigationBarHidden(false, animated: false)
        
        // 设置系统导航栏的右侧按钮
        let batchManageButton = UIButton(type: .custom)
        batchManageButton.setTitle("批量管理", for: .normal)
        batchManageButton.setTitleColor(UIColor(hex: "#666666"), for: .normal)
        batchManageButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        batchManageButton.addTarget(self, action: #selector(batchManageButtonTapped), for: .touchUpInside)
        
        // 添加一些样式
        batchManageButton.backgroundColor = UIColor(hex: "#F5F5F5")
        batchManageButton.layer.cornerRadius = 15
        batchManageButton.layer.masksToBounds = true
        
        // 计算文本宽度并设置按钮的尺寸
        let text = "批量管理"
        let font = UIFont.systemFont(ofSize: 14)
        let textWidth = (text as NSString).size(withAttributes: [NSAttributedString.Key.font: font]).width
        let buttonWidth = max(textWidth + 20, 80) // 确保最小宽度为 80
        
        batchManageButton.frame = CGRect(x: 0, y: 0, width: buttonWidth, height: 30)
        
        let rightBarButtonItem = UIBarButtonItem(customView: batchManageButton)
        navigationItem.rightBarButtonItem = rightBarButtonItem
        
        // 添加发布按钮
        setupPublishButton()
        
        // 获取视频分类
        fetchVideoTypeList()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 确保搜索框渐变边框的frame
        if let searchArea = contentView.subviews.first?.subviews.first,
           let gradientContainer = searchArea.subviews.first,
           let gradientLayer = gradientContainer.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = gradientContainer.bounds
        }
        
        // 确保发布按钮渐变背景的frame
        if let gradientLayer = publishButton.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = publishButton.bounds
        }
    }
    
    // 设置右上角批量管理按钮
    private func setupRightNavButton() {
        // 创建批量管理按钮
        batchManageButton = UIButton(type: .custom)
        batchManageButton.setTitle("批量管理", for: .normal)
        batchManageButton.setTitleColor(UIColor(hex: "#666666"), for: .normal)
        batchManageButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        batchManageButton.addTarget(self, action: #selector(batchManageButtonTapped), for: .touchUpInside)
        
        // 添加一些样式
        batchManageButton.backgroundColor = UIColor(hex: "#F5F5F5")
        batchManageButton.layer.cornerRadius = 15
        batchManageButton.layer.masksToBounds = true
        
        // 计算文本宽度并设置按钮的尺寸
        let text = "批量管理"
        let font = UIFont.systemFont(ofSize: 14)
        let textWidth = (text as NSString).size(withAttributes: [NSAttributedString.Key.font: font]).width
        let buttonWidth = max(textWidth + 20, 80) // 确保最小宽度为 80
        
        // 添加到导航栏
        addCustomRightButton(batchManageButton)
        
        // 设置按钮位置
        batchManageButton.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.centerY.equalToSuperview()
            make.height.equalTo(30)
            make.width.equalTo(buttonWidth)
        }
    }
    
    // 设置UI
    private func setupUI() {
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 设置搜索框
        setupSearchBar()
        
        // 设置分段控制器
        setupSegmentControl()
        
        // 设置操作按钮
        setupOperationButtons()
        
        // 设置表格视图
        setupTableView()
        
        // 添加点击手势以收起键盘
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTapGesture(_:)))
        tapGesture.cancelsTouchesInView = false
        view.addGestureRecognizer(tapGesture)
    }
    
    @objc private func handleTapGesture(_ gesture: UITapGestureRecognizer) {
        // 收起键盘
        view.endEditing(true)
        
        // 如果搜索框内容已变化，触发搜索
        checkAndTriggerSearch()
    }
    
    // 检查搜索内容是否变化并触发搜索
    private func checkAndTriggerSearch() {
        let newSearchText = searchBar.text ?? ""
        if newSearchText != currentSearchKeyword {
            currentSearchKeyword = newSearchText
            loadNewData()
        }
    }
    
    // 设置搜索框
    private func setupSearchBar() {
        // 创建一个容器视图来同时包含搜索框和筛选按钮
        let containerView = UIView()
        containerView.backgroundColor = UIColor(hex: "#F5F5F5")
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        contentView.addSubview(containerView)
        
        // 设置容器视图的位置
        containerView.snp.makeConstraints { make in
            make.top.equalTo(10)
            make.left.equalTo(16)
            make.right.equalTo(-16)
            make.height.equalTo(40)
        }
        
        // 创建搜索框的容器
        let searchArea = UIView()
        containerView.addSubview(searchArea)
        
        // 设置搜索框容器的位置
        searchArea.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.right.equalToSuperview().offset(-50) // 预留空间给筛选按钮
        }
        
        // 创建渐变背景视图作为搜索框的边框
        let gradientContainer = UIView()
        gradientContainer.layer.cornerRadius = 20
        searchArea.addSubview(gradientContainer)
        
        // 设置渐变容器的位置
        gradientContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview() // 填充整个搜索区域
        }
        
        // 添加渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.cornerRadius = 20
        gradientLayer.colors = [
            UIColor(hex: "#FB6602").cgColor,
            UIColor(hex: "#FB6602").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        gradientContainer.layer.addSublayer(gradientLayer)
        
        // 创建搜索框容器（白色背景）
        searchContainer = UIView()
        searchContainer.backgroundColor = UIColor(hex: "#F5F5F5")
        searchContainer.layer.cornerRadius = 19
        gradientContainer.addSubview(searchContainer)
        
        // 设置搜索框容器的位置（比渐变背景小一点，形成边框效果）
        searchContainer.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().offset(-2)
            make.height.equalToSuperview().offset(-2)
        }
        
        // 创建搜索图标
        let searchIcon = UIImageView(image: UIImage(named: "icon_search"))
        searchIcon.contentMode = .scaleAspectFit
        searchContainer.addSubview(searchIcon)
        
        // 创建搜索框
        searchBar = UITextField()
        searchBar.placeholder = "搜索标题、标签"
        searchBar.font = UIFont.systemFont(ofSize: 14)
        searchBar.borderStyle = .none
        searchBar.returnKeyType = .search
        searchBar.clearButtonMode = .whileEditing
        searchBar.delegate = self
        searchContainer.addSubview(searchBar)
        
        // 设置搜索图标的位置
        searchIcon.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        // 设置搜索框的位置
        searchBar.snp.makeConstraints { make in
            make.left.equalTo(searchIcon.snp.right).offset(8)
            make.right.equalTo(-12)
            make.top.bottom.equalToSuperview()
        }
        
        // 创建筛选按钮 - 添加到外部容器
        let filterButton = UIButton(type: .custom)
        filterButton.setImage(UIImage(named: "icon_filter"), for: .normal)
        filterButton.addTarget(self, action: #selector(filterButtonTapped), for: .touchUpInside)
        filterButton.backgroundColor = .white
        filterButton.layer.cornerRadius = 20
        // 添加轻微阴影效果
        filterButton.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        filterButton.layer.shadowOffset = CGSize(width: 0, height: 1)
        filterButton.layer.shadowRadius = 2
        filterButton.layer.shadowOpacity = 1
        containerView.addSubview(filterButton)
        
        // 设置筛选按钮的位置
        filterButton.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }
    }
    
    // 设置分段控制器
    private func setupSegmentControl() {
        // 创建分段控制器容器
        segmentContainer = UIView()
        segmentContainer.backgroundColor = .white
        contentView.addSubview(segmentContainer)
        
        // 设置分段控制器容器的位置
        segmentContainer.snp.makeConstraints { make in
            make.top.equalTo(searchContainer.snp.bottom).offset(10)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        // 分段选项标题
        let titles = ["全部内容", "已发布", "已下架", "审核中", "审核失败"]
        
        // 创建分段选项按钮
        for (index, title) in titles.enumerated() {
            let button = UIButton(type: .custom)
            button.setTitle(title, for: .normal)
            button.setTitleColor(UIColor(hex: "#999999"), for: .normal)
            button.setTitleColor(UIColor(hex: "#FB6602"), for: .selected) // 设置选中颜色为 #FB6602
            button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
            button.tag = index
            button.addTarget(self, action: #selector(segmentButtonTapped(_:)), for: .touchUpInside)
            button.backgroundColor = UIColor(hex: "#F5F5F5")
            segmentContainer.addSubview(button)
            segmentOptions.append(button)
            
            // 设置按钮位置
            let buttonWidth = UIScreen.main.bounds.width / CGFloat(titles.count)
            button.snp.makeConstraints { make in
                make.top.bottom.equalToSuperview()
                make.left.equalTo(buttonWidth * CGFloat(index))
                make.width.equalTo(buttonWidth)
            }
        }
        
        // 创建圆点指示器
        segmentIndicator = UIView()
        segmentIndicator.backgroundColor = UIColor(hex: "#FB6602")
        segmentIndicator.layer.cornerRadius = 3 // 设置为圆点
        segmentContainer.addSubview(segmentIndicator)
        segmentContainer.backgroundColor = UIColor(hex: "#FB6602")
        
        // 设置指示器位置 - 初始位置设置为第一个按钮
        segmentIndicator.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-5) // 距离底部5pt
            make.height.width.equalTo(6) // 圆点大小为6x6
            make.centerX.equalTo(segmentOptions[selectedSegmentIndex])
        }
        
        // 设置默认选中第一个选项
        segmentOptions[selectedSegmentIndex].isSelected = true
        segmentOptions[selectedSegmentIndex].titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .bold) // 选中时字体加粗
    }
    
    // 设置操作按钮
    private func setupOperationButtons() {
        // 创建操作按钮容器
        operationContainer = UIView()
        operationContainer.backgroundColor = .clear
        contentView.addSubview(operationContainer)
        
        // 设置操作按钮容器的位置
        operationContainer.snp.makeConstraints { make in
            make.top.equalTo(segmentContainer.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(50)
        }
        
        // 使用系统风格的选框 - 增大尺寸
        let checkboxSize: CGFloat = 26 // 从22增大到26
        let checkbox = UIView()
        checkbox.layer.borderWidth = 1
        checkbox.layer.borderColor = UIColor(hex: "#CCCCCC").cgColor
        checkbox.layer.cornerRadius = 4 // 保持与单元格选择框一致
        checkbox.backgroundColor = .white
        checkbox.tag = 101 // 用于识别选框
        operationContainer.addSubview(checkbox)
        
        // 创建全选文本标签
        let selectAllLabel = UILabel()
        selectAllLabel.text = "全选"
        selectAllLabel.textColor = UIColor(hex: "#333333")
        selectAllLabel.font = UIFont.systemFont(ofSize: 14)
        operationContainer.addSubview(selectAllLabel)
        
        // 设置选框位置
        checkbox.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(checkboxSize)
        }
        
        // 设置文本标签位置
        selectAllLabel.snp.makeConstraints { make in
            make.left.equalTo(checkbox.snp.right).offset(8)
            make.centerY.equalTo(checkbox)
        }
        
        // 创建一个透明的按钮覆盖选框和文字，用于捕获点击事件
        let selectAllButton = UIButton(type: .custom)
        selectAllButton.backgroundColor = .clear // 透明背景
        selectAllButton.addTarget(self, action: #selector(selectAllButtonTapped), for: .touchUpInside)
        operationContainer.addSubview(selectAllButton)
        
        // 设置按钮位置 - 覆盖选框和文字
        selectAllButton.snp.makeConstraints { make in
            make.left.equalTo(checkbox)
            make.right.equalTo(selectAllLabel).offset(10) // 稍微扩大点击区域
            make.top.bottom.equalToSuperview()
        }
        
        // 创建批量下架按钮 - 修改样式
        batchRemoveButton = UIButton(type: .custom)
        batchRemoveButton.setTitle("批量下架", for: .normal) // 初始标题，后续会根据分类动态更新
        batchRemoveButton.setTitleColor(UIColor(hex: "#777777"), for: .normal)
        batchRemoveButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        batchRemoveButton.backgroundColor = .white
        batchRemoveButton.layer.cornerRadius = 4  // 修改为4pt圆角
        batchRemoveButton.layer.borderWidth = 1
        batchRemoveButton.layer.borderColor = UIColor(hex: "#777777").cgColor
        batchRemoveButton.addTarget(self, action: #selector(batchRemoveButtonTapped), for: .touchUpInside)
        operationContainer.addSubview(batchRemoveButton)
        
        // 创建批量删除按钮 - 修改样式
        batchDeleteButton = UIButton(type: .custom)
        batchDeleteButton.setTitle("批量删除", for: .normal)
        batchDeleteButton.setTitleColor(UIColor(hex: "#FF3E4B"), for: .normal)
        batchDeleteButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        batchDeleteButton.backgroundColor = .white
        batchDeleteButton.layer.cornerRadius = 4  // 修改为4pt圆角
        batchDeleteButton.layer.borderWidth = 1
        batchDeleteButton.layer.borderColor = UIColor(hex: "#FF3E4B").cgColor
        batchDeleteButton.addTarget(self, action: #selector(batchDeleteButtonTapped), for: .touchUpInside)
        operationContainer.addSubview(batchDeleteButton)
        
        // 设置批量删除按钮位置
        batchDeleteButton.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(72)
            make.height.equalTo(32)
        }
        
        // 设置批量下架按钮位置
        batchRemoveButton.snp.makeConstraints { make in
            make.right.equalTo(batchDeleteButton.snp.left).offset(-10)
            make.centerY.equalToSuperview()
            make.width.equalTo(72)
            make.height.equalTo(32)
        }
        
        // 默认隐藏操作按钮容器
        operationContainer.isHidden = true
        
        // 根据当前分类更新批量下/上架按钮标题
        updateBatchRemoveButtonTitle()
    }
    
    // 更新批量下/上架按钮标题
    private func updateBatchRemoveButtonTitle() {
        guard batchRemoveButton != nil else { return }
        if currentContentStatus == 2 { // 已下架分类
            batchRemoveButton.setTitle("批量上架", for: .normal)
        } else {
            batchRemoveButton.setTitle("批量下架", for: .normal)
        }
    }
    
    // 设置表格视图
    private func setupTableView() {
        // 创建表格视图
        tableView = UITableView(frame: .zero, style: .plain)
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.delegate = self
        tableView.dataSource = self
        
        // 注册自定义单元格
        tableView.register(ContentManagementCell.self, forCellReuseIdentifier: "ContentManagementCell")
        
        contentView.addSubview(tableView)
        
        // 设置表格视图的位置
        tableView.snp.makeConstraints { make in
            make.top.equalTo(segmentContainer.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        // 设置下拉刷新和上拉加载
        setupRefreshControls()
        
        // 加载数据
        loadContentList(isRefresh: true)
        
        // 调整表格视图的底部内容边距，确保最后一个cell不被发布按钮遮挡
        tableView.contentInset.bottom = 46 + 12 + 12 // 按钮高度 + 上边距 + 额外边距
    }
    
    // 设置刷新控件
    private func setupRefreshControls() {
        // 设置下拉刷新
        let header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(loadNewData))
        header.setTitle("下拉刷新", for: .idle)
        header.setTitle("松开刷新", for: .pulling)
        header.setTitle("正在刷新", for: .refreshing)
        tableView.mj_header = header
        
        // 设置上拉加载
        let footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(loadMoreData))
        footer.setTitle("", for: .idle)
        footer.setTitle("正在加载更多...", for: .refreshing)
        footer.setTitle("没有更多内容了", for: .noMoreData)
        tableView.mj_footer = footer
    }
    
    // 下拉刷新
    @objc private func loadNewData() {
        currentPage = 0
        hasMoreData = true
        isLoading = false // 重置加载状态
        // 如果存在上拉加载控件，重置"没有更多数据"状态
        tableView.mj_footer?.resetNoMoreData()
        loadContentList(isRefresh: true)
    }
    
    // 上拉加载
    @objc private func loadMoreData() {
        if hasMoreData && !isLoading {
            // 直接使用currentPage，因为在handleContentListResponse中会更新currentPage
            loadContentList(page: currentPage, isRefresh: false)
        } else if !hasMoreData {
            tableView.mj_footer?.endRefreshingWithNoMoreData()
        } else {
            tableView.mj_footer?.endRefreshing()
        }
    }
    
    // 加载内容列表
    private func loadContentList(page: Int = 0, isRefresh: Bool = false) {
        if isLoading { return }
        isLoading = true

        // 确定实际使用的页码
        let actualPage: Int
        if isRefresh {
            currentPage = 0
            actualPage = 0
        } else {
            actualPage = page
        }

        print("开始获取内容列表，实际页码: \(actualPage), 状态: \(currentContentStatus), 搜索关键词: \(currentSearchKeyword)")
        
        // 根据分段控制器选择的状态获取对应状态的内容
        // 状态映射: 0=全部, 1=已发布, 2=已下架, 3=审核中 4=审核失败
        let statusParams = getStatusParams()
        
        // 从状态参数中获取state值
        let state = statusParams["state"] as? Int
        
        // 用于日志输出的参数汇总
        var logParams = "页码: \(actualPage), 每页数量: \(pageSize)"
        if let state = state { logParams += ", 状态: \(state)" }
        if !currentSearchKeyword.isEmpty { logParams += ", 关键词: \(currentSearchKeyword)" }
        if let sort = currentSort { logParams += ", 排序: \(sort)" }
        if let time = currentTime { logParams += ", 时间: \(time)" }
        if let duration = currentDuration { logParams += ", 时长: \(duration)" }
        if let categoryId = currentWorksCategoryId { logParams += ", 分类ID: \(categoryId)" }

        print("请求参数: \(logParams)")

        // 调用API获取内容列表，传入所有需要的参数
        APIManager.shared.getPersonalWorksList(
            page: actualPage,
            size: pageSize,
            duration: currentDuration,
            keywords: !currentSearchKeyword.isEmpty ? currentSearchKeyword : nil,
            sort: currentSort,
            state: state,
            time: currentTime,
            worksCategoryId: currentWorksCategoryId
        ) { [weak self] result in
            guard let self = self else { return }
            
            self.isLoading = false
            
            // 结束刷新状态
            if isRefresh {
                self.tableView.mj_header?.endRefreshing()
            } else {
                self.tableView.mj_footer?.endRefreshing()
            }
            
            switch result {
            case .success(let response):
                if let data = response.data {
                    // 处理返回的数据
                    self.handleContentListResponse(data: data, isRefresh: isRefresh)
                } else {
                    print("API响应中没有数据字段或数据为空")
                    // 如果没有数据，显示空状态
                    if isRefresh {
                        self.contentItems = []
                        self.tableView.reloadData()
                    }
                    self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                }
                
            case .failure(let error):
                print("获取内容列表失败: \(error.localizedDescription)")
                // 显示错误提示
                self.showToast("获取内容列表失败，请稍后重试")
            }
        }
    }
    
    // 处理内容列表响应
    private func handleContentListResponse(data: PersonalWorksListData, isRefresh: Bool) {
        // 转换API返回的数据为ContentManagementItem
        let newItems = convertToContentItems(from: data.list)

        // 如果是刷新，清空现有数据
        if isRefresh {
            contentItems = newItems
            currentPage = 0 // 重置页码
        } else {
            // 否则追加数据
            contentItems.append(contentsOf: newItems)
        }

        print("获取内容列表成功，总数量: \(data.total), 当前页码: \(currentPage), 当前列表中共有 \(contentItems.count) 项")

        // 更新分页信息 - 只有在成功加载数据后才增加页码
        if !newItems.isEmpty {
            currentPage = currentPage + 1
        }

        // 判断是否有更多数据
        hasMoreData = contentItems.count < data.total

        print("分页状态更新: currentPage=\(currentPage), hasMoreData=\(hasMoreData), contentItems.count=\(contentItems.count), total=\(data.total)")

        // 更新上拉加载状态
        if hasMoreData {
            tableView.mj_footer?.endRefreshing()
        } else {
            tableView.mj_footer?.endRefreshingWithNoMoreData()
        }

        // 更新表格视图
        tableView.reloadData()
    }
    
    // 将API返回的数据转换为ContentManagementItem
    private func convertToContentItems(from worksList: [PersonalWorksListDetailData]) -> [ContentManagementItem] {
        return worksList.map { work in
            // 确定发布状态
            let publishStatus: ContentManagementPublishStatus
            switch work.state {
            case 2: // 假设 2 表示已发布
                publishStatus = .published
            case 3: // 假设 3 表示已下架
                publishStatus = .unpublished
            case 1: // 假设 1 表示审核中
                publishStatus = .reviewing
            case 4: // 假设 4 表示审核失败
                publishStatus = .reviewFailed
            default:
                publishStatus = .reviewing // 默认为审核中
            }
            
            // 处理标签，这里假设worksCategoryName是标签，实际情况可能需要调整
            let tags = work.worksCategoryName != nil ? [work.worksCategoryName] : []
            
            // 处理封面图片URL
            var coverImageUrl = work.worksCoverImg
            if !coverImageUrl.isEmpty && !coverImageUrl.hasPrefix("http") {
                // 添加基础URL前缀
                coverImageUrl = "https://test-youshu.gzyoushu.com/video" + coverImageUrl
            }
            
            // 过滤掉nil值
            let validTags = tags.compactMap { $0 }
            
            // 处理发布时间
            let publishTime = work.updateTime ?? ""
            
            return ContentManagementItem(
                id: "\(work.id)",
                title: work.worksTitle,
                tags: validTags,
                viewCount: work.watchNumber ?? 0,
                likeCount: work.likeNumber ?? 0,
                isSelected: false,
                publishStatus: publishStatus,
                coverImageUrl: coverImageUrl,
                updateTime: publishTime,
                refuseReason: work.examineRefuseReason ?? ""
            )
        }
    }
    
    // 根据当前选中的状态获取对应的API参数
    private func getStatusParams() -> [String: Any] {
        switch currentContentStatus {
        case 1: // 已发布
            return ["state": 2] // 假设API中state=2表示已发布
        case 2: // 已下架
            return ["state": 3] // 假设API中state=3表示已下架
        case 3: // 审核中
            return ["state": 1] // 假设API中state=1表示审核中
        case 4: // 审核失败
            return ["state": 4] // 假设API中state=4表示审核失败
        default: // 全部内容
            return [:]
        }
    }
    
    // MARK: - Actions
    
    // 分段按钮点击事件
    @objc private func segmentButtonTapped(_ sender: UIButton) {
        // 更新选中状态
        segmentOptions.forEach {
            $0.isSelected = false
            $0.titleLabel?.font = UIFont.systemFont(ofSize: 14) // 恢复普通字体
        }
        sender.isSelected = true
        sender.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .bold) // 选中时字体加粗
        selectedSegmentIndex = sender.tag
        
        // 更新圆点指示器位置
        segmentIndicator.snp.remakeConstraints { make in
            make.bottom.equalToSuperview().offset(-5) // 距离底部5pt
            make.height.width.equalTo(6) // 保持圆点大小为6x6
            make.centerX.equalTo(segmentOptions[selectedSegmentIndex])
        }
        
        // 使用动画更新布局
        UIView.animate(withDuration: 0.3) {
            self.segmentContainer.layoutIfNeeded()
        }
        
        // 更新当前内容状态并刷新数据
        currentContentStatus = selectedSegmentIndex
        updateBatchRemoveButtonTitle()
        loadNewData() // 重新加载数据
    }
    
    // 筛选按钮点击事件
    @objc private func filterButtonTapped() {
        print("筛选按钮被点击")
        showFilterMenu()
    }
    
    // 显示自定义侧边筛选菜单
    private func showFilterMenu() {
        // 创建滤镜菜单容器（覆盖整个屏幕）
        let filterContainer = UIView(frame: view.bounds)
        filterContainer.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        filterContainer.alpha = 0
        view.addSubview(filterContainer)
        
        // 添加点击背景关闭的手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissFilterMenu))
        filterContainer.addGestureRecognizer(tapGesture)
        filterContainer.tag = 1001 // 用于识别和移除
        
        // 创建滤镜菜单 - 修改宽度和左边距
        let leftMargin: CGFloat = 90 // 左侧留出的距离
        let menuWidth: CGFloat = view.bounds.width - leftMargin
        let menuView = UIView(frame: CGRect(x: view.bounds.width, y: 0, width: menuWidth, height: view.bounds.height))
        menuView.backgroundColor = .white
        filterContainer.addSubview(menuView)
        
        // 阻止点击事件传递到背景
        let menuTapGesture = UITapGestureRecognizer(target: nil, action: nil)
        menuTapGesture.cancelsTouchesInView = false
        menuView.addGestureRecognizer(menuTapGesture)
        
        // 创建标题栏
        let titleBar = UIView(frame: CGRect(x: 0, y: 0, width: menuWidth, height: 60))
        titleBar.backgroundColor = .white
        menuView.addSubview(titleBar)
        
        // 添加返回按钮
        let backButton = UIButton(type: .custom)
        backButton.frame = CGRect(x: 16, y: 8, width: 44, height: 44)
        backButton.setImage(UIImage(named: "icon_back"), for: .normal)
        backButton.addTarget(self, action: #selector(dismissFilterMenu), for: .touchUpInside)
        titleBar.addSubview(backButton)
        
        // 添加标题
        let titleLabel = UILabel(frame: CGRect(x: 82, y: 18, width: menuWidth - 120, height: 24))
        titleLabel.text = "" // 修改为通用标题，避免与下方的类别标题重复
        titleLabel.textAlignment = .left
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleBar.addSubview(titleLabel)
        
        // 删除多余的搜索头部区域，直接创建滚动视图
        let scrollView = UIScrollView(frame: CGRect(x: 0, y: titleBar.frame.maxY, width: menuWidth, height: view.bounds.height - titleBar.frame.maxY))
        scrollView.showsVerticalScrollIndicator = false
        menuView.addSubview(scrollView)
        
        // 初始化临时筛选条件为当前筛选条件
        tempSort = currentSort
        tempWorksCategoryId = currentWorksCategoryId
        tempTime = currentTime
        tempDuration = currentDuration
        
        // 添加筛选项
        setupFilterOptionsWithDesign(in: scrollView, width: menuWidth)
        
        // 显示动画
        UIView.animate(withDuration: 0.3) {
            filterContainer.alpha = 1
            menuView.frame.origin.x = leftMargin
        }
    }
    
    // 设置筛选选项 - 按照设计图实现
    private func setupFilterOptionsWithDesign(in scrollView: UIScrollView, width: CGFloat) {
        let padding: CGFloat = 20
        var yOffset: CGFloat = 20
        
        // 排序方式标题
        let sortTitle = UILabel(frame: CGRect(x: padding, y: yOffset, width: width - padding * 2, height: 24))
        sortTitle.text = "排序方式"
        sortTitle.textColor = UIColor(hex: "#333333")
        sortTitle.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        scrollView.addSubview(sortTitle)
        
        yOffset += sortTitle.frame.height + 16
        
        // 统一所有按钮样式参数
        let buttonHeight: CGFloat = 34
        let buttonSpacing: CGFloat = 10
        let buttonsPerRow = 3
        let buttonWidth = (width - padding * 2 - buttonSpacing * CGFloat(buttonsPerRow - 1)) / CGFloat(buttonsPerRow)
        
        // 排序方式选项 - 改为与其他按钮相同样式
        let sortOptions = ["最新发布", "最多播放", "最多点赞"]
        let sortValues = [1, 2, 3] // 对应API的sort值
        
        var currentRow = 0
        var currentColumn = 0
        
        for (index, option) in sortOptions.enumerated() {
            currentRow = index / buttonsPerRow
            currentColumn = index % buttonsPerRow
            
            // 设置默认选中的按钮
            let isOptionSelected: Bool
            if let currentSortValue = tempSort {
                isOptionSelected = currentSortValue == sortValues[index]
            } else {
                isOptionSelected = index == 0 // 默认选中第一个
            }
            
            let button = createPillButton(
                title: option,
                frame: CGRect(
                    x: padding + CGFloat(currentColumn) * (buttonWidth + buttonSpacing),
                    y: yOffset + CGFloat(currentRow) * (buttonHeight + buttonSpacing),
                    width: buttonWidth,
                    height: buttonHeight
                ),
                tag: 1000 + index,
                sortValue: sortValues[index], // 保存sort值
                isSelected: isOptionSelected
            )
            scrollView.addSubview(button)
        }
        
        // 计算排序选项占用的行数
        let sortRowCount = Int(ceil(Double(sortOptions.count) / Double(buttonsPerRow)))
        yOffset += CGFloat(sortRowCount) * (buttonHeight + buttonSpacing) + 10
        
        // 添加分割线
        let divider = UIView(frame: CGRect(x: padding, y: yOffset, width: width - padding * 2, height: 1))
        divider.backgroundColor = UIColor(hex: "#F5F5F5")
        scrollView.addSubview(divider)
        
        yOffset += divider.frame.height + 20
        
        // 视频类型标题
        let typeTitle = UILabel(frame: CGRect(x: padding, y: yOffset, width: width - padding * 2, height: 24))
        typeTitle.text = "视频类型"
        typeTitle.textColor = UIColor(hex: "#333333")
        typeTitle.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        scrollView.addSubview(typeTitle)
        
        yOffset += typeTitle.frame.height + 16
        
        // 视频类型选项 - 为每个类型分配唯一ID
        let videoTypes = videoTypeTitles
        let typeValues = videoTypeIds
        
        currentRow = 0
        currentColumn = 0
        
        for (index, type) in videoTypes.enumerated() {
            currentRow = index / buttonsPerRow
            currentColumn = index % buttonsPerRow
            
            // 设置默认选中的按钮 - 只有与当前分类ID匹配的选项才会被选中
            let isOptionSelected: Bool
            if let categoryId = tempWorksCategoryId {
                isOptionSelected = categoryId == typeValues[index]
            } else {
                isOptionSelected = index == 0 // 默认选中"全部"
            }
            
            let button = createPillButton(
                title: type,
                frame: CGRect(
                    x: padding + CGFloat(currentColumn) * (buttonWidth + buttonSpacing),
                    y: yOffset + CGFloat(currentRow) * (buttonHeight + buttonSpacing),
                    width: buttonWidth,
                    height: buttonHeight
                ),
                tag: 2000 + index,
                categoryId: typeValues[index],
                isSelected: isOptionSelected
            )
            scrollView.addSubview(button)
        }
        
        // 计算视频类型占用的行数
        let typeRowCount = Int(ceil(Double(videoTypes.count) / Double(buttonsPerRow)))
        yOffset += CGFloat(typeRowCount) * (buttonHeight + buttonSpacing) + 20
        
        // 添加分割线
        let divider2 = UIView(frame: CGRect(x: padding, y: yOffset, width: width - padding * 2, height: 1))
        divider2.backgroundColor = UIColor(hex: "#F5F5F5")
        scrollView.addSubview(divider2)
        
        yOffset += divider2.frame.height + 20
        
        // 发布时间标题
        let timeTitle = UILabel(frame: CGRect(x: padding, y: yOffset, width: width - padding * 2, height: 24))
        timeTitle.text = "发布时间"
        timeTitle.textColor = UIColor(hex: "#333333")
        timeTitle.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        scrollView.addSubview(timeTitle)
        
        yOffset += timeTitle.frame.height + 16
        
        // 发布时间选项
        let timeOptions = ["全部", "今天", "本周", "本月"]
        let timeValues = [nil, 1, 2, 3] // 对应API的time值
        
        currentRow = 0
        currentColumn = 0
        
        for (index, time) in timeOptions.enumerated() {
            currentRow = index / buttonsPerRow
            currentColumn = index % buttonsPerRow
            
            // 设置默认选中的按钮
            let isOptionSelected: Bool
            if let currentTimeValue = tempTime {
                isOptionSelected = currentTimeValue == timeValues[index]
            } else {
                isOptionSelected = index == 0 // 默认选中"全部"
            }
            
            let button = createPillButton(
                title: time,
                frame: CGRect(
                    x: padding + CGFloat(currentColumn) * (buttonWidth + buttonSpacing),
                    y: yOffset + CGFloat(currentRow) * (buttonHeight + buttonSpacing),
                    width: buttonWidth,
                    height: buttonHeight
                ),
                tag: 3000 + index,
                timeValue: timeValues[index], // 保存time值
                isSelected: isOptionSelected
            )
            scrollView.addSubview(button)
        }
        
        // 计算时间选项占用的行数
        let timeRowCount = Int(ceil(Double(timeOptions.count) / Double(buttonsPerRow)))
        yOffset += CGFloat(timeRowCount) * (buttonHeight + buttonSpacing) + 20
        
        // 添加分割线
        let divider3 = UIView(frame: CGRect(x: padding, y: yOffset, width: width - padding * 2, height: 1))
        divider3.backgroundColor = UIColor(hex: "#F5F5F5")
        scrollView.addSubview(divider3)
        
        yOffset += divider3.frame.height + 20
        
        // 视频长度标题
        let durationTitle = UILabel(frame: CGRect(x: padding, y: yOffset, width: width - padding * 2, height: 24))
        durationTitle.text = "视频长度"
        durationTitle.textColor = UIColor(hex: "#333333")
        durationTitle.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        scrollView.addSubview(durationTitle)
        
        yOffset += durationTitle.frame.height + 16
        
        // 视频长度选项
        let durationOptions = ["全部", "60秒以下", "1-3分钟", "3分钟以上"]
        let durationValues = [nil, 1, 2, 3] // 对应API的duration值
        
        currentRow = 0
        currentColumn = 0
        
        for (index, duration) in durationOptions.enumerated() {
            currentRow = index / buttonsPerRow
            currentColumn = index % buttonsPerRow
            
            // 设置默认选中的按钮
            let isOptionSelected: Bool
            if let currentDurationValue = tempDuration {
                isOptionSelected = currentDurationValue == durationValues[index]
            } else {
                isOptionSelected = index == 0 // 默认选中"全部"
            }
            
            let button = createPillButton(
                title: duration,
                frame: CGRect(
                    x: padding + CGFloat(currentColumn) * (buttonWidth + buttonSpacing),
                    y: yOffset + CGFloat(currentRow) * (buttonHeight + buttonSpacing),
                    width: buttonWidth,
                    height: buttonHeight
                ),
                tag: 4000 + index,
                durationValue: durationValues[index], // 保存duration值
                isSelected: isOptionSelected
            )
            scrollView.addSubview(button)
        }
        
        // 计算时长选项占用的行数
        let durationRowCount = Int(ceil(Double(durationOptions.count) / Double(buttonsPerRow)))
        yOffset += CGFloat(durationRowCount) * (buttonHeight + buttonSpacing) + 30
        
        // 添加重置和确定按钮
        let resetWidth: CGFloat = (width - padding * 3) / 2
        let confirmButtonHeight: CGFloat = 44 // 防止变量重复声明
        
        let resetButton = UIButton(type: .custom)
        resetButton.frame = CGRect(x: padding, y: yOffset, width: resetWidth, height: confirmButtonHeight)
        resetButton.setTitle("重置", for: .normal)
        resetButton.setTitleColor(UIColor(hex: "#666666"), for: .normal)
        resetButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        resetButton.backgroundColor = UIColor(hex: "#F5F5F5")
        resetButton.layer.cornerRadius = confirmButtonHeight / 2
        resetButton.addTarget(self, action: #selector(resetFilterOptions), for: .touchUpInside)
        scrollView.addSubview(resetButton)
        
        let confirmButton = UIButton(type: .custom)
        confirmButton.frame = CGRect(x: padding * 2 + resetWidth, y: yOffset, width: resetWidth, height: confirmButtonHeight)
        confirmButton.setTitle("确定", for: .normal)
        confirmButton.setTitleColor(.white, for: .normal)
        confirmButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        confirmButton.backgroundColor = UIColor(hex: "#FB6602")
        confirmButton.layer.cornerRadius = confirmButtonHeight / 2
        confirmButton.addTarget(self, action: #selector(applyFilterOptions), for: .touchUpInside)
        scrollView.addSubview(confirmButton)
        
        yOffset += confirmButtonHeight + padding
        
        // 设置滚动视图的内容大小
        scrollView.contentSize = CGSize(width: width, height: yOffset)
    }
    
    // 创建胶囊型按钮 - 增加参数以保存筛选值
    private func createPillButton(
        title: String,
        frame: CGRect,
        tag: Int,
        sortValue: Int? = nil,
        categoryId: String? = nil,
        timeValue: Int? = nil,
        durationValue: Int? = nil,
        isSelected: Bool
    ) -> UIButton {
        let button = UIButton(type: .custom)
        button.frame = frame
        button.setTitle(title, for: .normal)
        
        // 修改颜色设置
        button.setTitleColor(UIColor(hex: "#FB6602"), for: .selected) // 选中状态文字颜色为 #FB6602
        button.setTitleColor(UIColor(hex: "#666666"), for: .normal) // 未选中状态文字颜色保持原样
        
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        
        // 设置背景色
        button.backgroundColor = isSelected ? UIColor(hex: "#FFF2EB") : UIColor(hex: "#F5F5F5")
        
        // 设置边框 - 选中和未选中状态
        button.layer.borderWidth = 1
        button.layer.borderColor = isSelected ? UIColor(hex: "#FB6602").cgColor : UIColor(hex: "#EBEBEB").cgColor
        
        button.layer.cornerRadius = frame.height / 2
        button.tag = tag
        button.isSelected = isSelected
        
        // 存储筛选值到按钮
        if let sortValue = sortValue {
            button.accessibilityIdentifier = "sort_\(sortValue)"
        } else if let categoryId = categoryId {
            button.accessibilityIdentifier = "category_\(categoryId)"
        } else if let timeValue = timeValue {
            button.accessibilityIdentifier = "time_\(timeValue)"
        } else if let durationValue = durationValue {
            button.accessibilityIdentifier = "duration_\(durationValue)"
        }
        
        button.addTarget(self, action: #selector(filterOptionSelected(_:)), for: .touchUpInside)
        return button
    }
    
    // 修改筛选选项选中方法 - 更新临时筛选条件而不是直接应用
    @objc private func filterOptionSelected(_ sender: UIButton) {
        // 确定选项类别
        let categoryStart: Int
        if sender.tag >= 1000 && sender.tag < 2000 {
            categoryStart = 1000 // 排序方式
        } else if sender.tag >= 2000 && sender.tag < 3000 {
            categoryStart = 2000 // 视频类型
        } else if sender.tag >= 3000 && sender.tag < 4000 {
            categoryStart = 3000 // 发布时间
        } else if sender.tag >= 4000 && sender.tag < 5000 {
            categoryStart = 4000 // 视频长度
        } else {
            return
        }
        
        if let containerView = sender.superview {
            // 处理互斥选择
            for subview in containerView.subviews {
                if let button = subview as? UIButton {
                    // 同组选项互斥
                    if button.tag >= categoryStart && button.tag < categoryStart + 1000 {
                        button.isSelected = false
                        button.backgroundColor = UIColor(hex: "#F5F5F5")
                        button.layer.borderColor = UIColor(hex: "#EBEBEB").cgColor // 未选中状态边框为浅灰色
                    }
                }
            }
        }
        
        // 设置当前选中状态
        sender.isSelected = true
        sender.backgroundColor = UIColor(hex: "#FFF2EB") // 修改为新的背景色
        sender.layer.borderColor = UIColor(hex: "#FB6602").cgColor // 修改为新的边框色
        
        // 更新临时筛选条件
        if let identifier = sender.accessibilityIdentifier {
            if identifier.hasPrefix("sort_") {
                let sortValue = Int(identifier.dropFirst(5))
                tempSort = sortValue
            } else if identifier.hasPrefix("category_") {
                let categoryId = String(identifier.dropFirst(9))
                tempWorksCategoryId = categoryId.isEmpty ? nil : categoryId
            } else if identifier.hasPrefix("time_") {
                let timeValue = Int(identifier.dropFirst(5))
                tempTime = timeValue
            } else if identifier.hasPrefix("duration_") {
                let durationValue = Int(identifier.dropFirst(9))
                tempDuration = durationValue
            }
        } else if sender.tag == 2000 { // 视频类型"全部"选项
            tempWorksCategoryId = nil
        } else if sender.tag == 3000 { // 发布时间"全部"选项
            tempTime = nil
        } else if sender.tag == 4000 { // 视频长度"全部"选项
            tempDuration = nil
        }
    }
    
    // 更新重置筛选选项方法
    @objc private func resetFilterOptions() {
        // 重置临时筛选条件
        tempSort = nil
        tempWorksCategoryId = nil
        tempTime = nil
        tempDuration = nil
        
        // 重置UI
        if let filterContainer = view.viewWithTag(1001),
           let scrollView = filterContainer.subviews.compactMap({ $0.subviews.first(where: { $0 is UIScrollView }) }).first as? UIScrollView {
            
            for subview in scrollView.subviews {
                if let button = subview as? UIButton {
                    let tag = button.tag
                    
                    // 所有类别的第一个选项都重置为选中状态
                    if tag == 1000 || tag == 2000 || tag == 3000 || tag == 4000 {
                        button.isSelected = true
                        button.backgroundColor = UIColor(hex: "#FFF2EB") // 更新为新的选中背景色
                        button.layer.borderColor = UIColor(hex: "#FB6602").cgColor // 更新为新的边框色
                    } else if (tag > 1000 && tag < 2000) || (tag > 2000 && tag < 3000) ||
                              (tag > 3000 && tag < 4000) || (tag > 4000 && tag < 5000) {
                        // 其他选项都重置为未选中状态
                        button.isSelected = false
                        button.backgroundColor = UIColor(hex: "#F5F5F5")
                        button.layer.borderColor = UIColor(hex: "#EBEBEB").cgColor // 未选中状态边框为浅灰色
                    }
                }
            }
        }
        
        // 直接应用重置后的筛选条件
        currentSort = nil
        currentWorksCategoryId = nil
        currentTime = nil
        currentDuration = nil
        
        // 打印日志，显示已重置筛选条件
        print("重置筛选：所有筛选条件已清除")
        
        // 执行网络请求
        loadNewData()
        
        // 关闭筛选菜单
        dismissFilterMenu()
    }
    
    // 应用筛选选项 - 确认选择并应用到实际筛选条件
    @objc private func applyFilterOptions() {
        // 从临时筛选条件更新实际筛选条件
        currentSort = tempSort
        currentWorksCategoryId = tempWorksCategoryId
        currentTime = tempTime
        currentDuration = tempDuration
        
        // 打印日志，显示应用的筛选条件
        var appliedFilters = "应用筛选: "
        if let sort = currentSort { appliedFilters += "排序方式[\(sort)], " }
        if let categoryId = currentWorksCategoryId { appliedFilters += "视频类型ID[\(categoryId)], " }
        if let time = currentTime { appliedFilters += "发布时间[\(time)], " }
        if let duration = currentDuration { appliedFilters += "视频长度[\(duration)]" }
        print(appliedFilters)
        
        // 重新加载数据
        loadNewData()
        
        // 关闭筛选菜单
        dismissFilterMenu()
    }
    
    // 关闭筛选菜单 - 不应用未确认的筛选条件
    @objc private func dismissFilterMenu() {
        if let filterContainer = view.viewWithTag(1001) {
            UIView.animate(withDuration: 0.3, animations: {
                filterContainer.alpha = 0
                if let menuView = filterContainer.subviews.first(where: { $0 is UIView && $0.tag != 1001 }) {
                    menuView.frame.origin.x = self.view.bounds.width
                }
            }) { _ in
                filterContainer.removeFromSuperview()
            }
        }
        
        // 放弃临时筛选条件（如果没有点击确定按钮）
        // 这里不需要重新赋值，因为dismissFilterMenu时临时变量未被应用
        // 如果需要重置临时变量，应该在下次showFilterMenu时进行
    }
    
    // 批量管理按钮点击事件
    @objc private func batchManageButtonTapped() {
        // 切换批量管理模式
        isBatchManageMode = !isBatchManageMode
        
        // 切换操作按钮容器的显示状态
        operationContainer.isHidden = !isBatchManageMode
        
        // 更新表格视图的约束
        tableView.snp.remakeConstraints { make in
            if !isBatchManageMode {
                make.top.equalTo(segmentContainer.snp.bottom)
            } else {
                make.top.equalTo(operationContainer.snp.bottom)
            }
            make.left.right.bottom.equalToSuperview()
        }
        
        // 确保底部内容边距不变
        tableView.contentInset.bottom = 46 + 12 + 12 // 按钮高度 + 上边距 + 额外边距
        
        // 更新批量管理按钮的标题
        let newTitle = !isBatchManageMode ? "批量管理" : "完成"
        batchManageButton.setTitle(newTitle, for: .normal)
        
        // 计算文本宽度
        let font = UIFont.systemFont(ofSize: 14)
        let textWidth = (newTitle as NSString).size(withAttributes: [NSAttributedString.Key.font: font]).width
        let buttonWidth = max(textWidth + 20, 80) // 确保最小宽度为 80
        
        // 更新按钮宽度
        var buttonFrame = batchManageButton.frame
        buttonFrame.size.width = buttonWidth
        batchManageButton.frame = buttonFrame
        
        // 进入或退出批量管理模式时重置所有选择状态
        if !isBatchManageMode {
            // 退出批量管理模式，重置所有选择状态
            for i in 0..<contentItems.count {
                contentItems[i].isSelected = false
            }
            
            // 重置全选状态
            isSelectAll = false
            if let checkbox = operationContainer.viewWithTag(101) {
                updateCheckbox(checkbox, isSelected: false)
            }
        }
        
        // 更新发布按钮的显示状态
        publishButton.isHidden = isBatchManageMode
        
        // 立即刷新表格，确保选择框的显示/隐藏状态正确更新
        tableView.reloadData()
        
        // 立即更新布局
        UIView.performWithoutAnimation {
            self.view.layoutIfNeeded()
        }
    }
    
    // 全选按钮点击事件
    @objc private func selectAllButtonTapped() {
        // 切换全选状态
        isSelectAll = !isSelectAll
        
        // 更新选框状态
        if let checkbox = operationContainer.viewWithTag(101) {
            updateCheckbox(checkbox, isSelected: isSelectAll)
        }
        
        // 更新所有内容项的选中状态
        for i in 0..<contentItems.count {
            contentItems[i].isSelected = isSelectAll
        }
        
        // 刷新表格
        tableView.reloadData()
    }
    
    // 批量下架按钮点击事件
    @objc private func batchRemoveButtonTapped() {
        let selectedItems = contentItems.filter { $0.isSelected }
        if selectedItems.isEmpty {
            let tip = (currentContentStatus == 2) ? "请先选择要上架的内容" : "请先选择要下架的内容"
            showToast(tip)
            return
        }

        // 构建ID列表
        let selectedIds = selectedItems.map { $0.id }

        // 根据分类决定操作
        let isPublishAction = (currentContentStatus == 2) // 已下架列表 -> 上架
        let actionName = isPublishAction ? "上架" : "下架"
        let operationType = isPublishAction ? 2 : 1

        print("批量\(actionName)按钮被点击，选中了 \(selectedItems.count) 项，ID: \(selectedIds)")

        // 确认弹窗
        let alertView = CommonAlertView(
            title: "确认\(actionName)",
            message: "确定要\(actionName)选中的 \(selectedItems.count) 项内容吗？",
            leftButtonTitle: "取消",
            rightButtonTitle: "确定"
        )
        alertView.onLeftButtonTap = { [weak alertView] in
            alertView?.dismiss()
        }
        alertView.onRightButtonTap = { [weak self, weak alertView] in
            alertView?.dismiss()
            self?.performBatchOperate(ids: selectedIds, operationType: operationType)
        }
        alertView.show(in: self.view)
    }

    // 执行批量上/下架
    private func performBatchOperate(ids: [String], operationType: Int) {
        // 显示加载提示
        let loadingHUD = UIActivityIndicatorView(style: .large)
        loadingHUD.color = UIColor(hex: "#FB6602")
        loadingHUD.startAnimating()
        view.addSubview(loadingHUD)
        loadingHUD.center = view.center
        
        // 调用批量上下架API
        APIManager.shared.shortVideoWorksOperate(type: operationType, worksIds: ids) { [weak self] result in
            loadingHUD.removeFromSuperview()
            
            switch result {
            case .success(_):
                let successMsg = operationType == 2 ? "已成功上架 \(ids.count) 项内容" : "已成功下架 \(ids.count) 项内容"
                self?.showToast(successMsg)
                
                // 更新本地数据状态
                for (index, item) in self?.contentItems.enumerated() ?? [].enumerated() {
                    if ids.contains(item.id) {
                        self?.contentItems[index].publishStatus = (operationType == 2 ? .published : .unpublished)
                    }
                }
                
                // 刷新表格
                self?.tableView.reloadData()
                
                // 重置批量管理状态
                self?.batchManageButtonTapped()
                
            case .failure(let error):
                let failMsg = operationType == 2 ? "上架失败" : "下架失败"
                self?.showToast("\(failMsg): \(error.localizedDescription)")
            }
        }
    }
    
    // 批量删除按钮点击事件
    @objc private func batchDeleteButtonTapped() {
        let selectedItems = contentItems.filter { $0.isSelected }
        if selectedItems.isEmpty {
            showToast("请先选择要删除的内容")
            return
        }
        
        // 构建要删除的内容ID列表
        let selectedIds = selectedItems.map { $0.id }
        
        // 显示确认对话框
        let deleteAlert = CommonAlertView(
            title: "确认删除",
            message: "确定要删除选中的 \(selectedItems.count) 项内容吗？此操作不可恢复！",
            leftButtonTitle: "取消",
            rightButtonTitle: "删除"
        )
        deleteAlert.onLeftButtonTap = { [weak deleteAlert] in deleteAlert?.dismiss() }
        deleteAlert.onRightButtonTap = { [weak self, weak deleteAlert] in
            deleteAlert?.dismiss()
            self?.performBatchDeletion(ids: selectedIds)
        }
        deleteAlert.show(in: self.view)
    }
    
    // 执行批量删除
    private func performBatchDeletion(ids: [String]) {
        // 显示加载提示
        let loadingHUD = UIActivityIndicatorView(style: .large)
        loadingHUD.color = UIColor(hex: "#FB6602")
        loadingHUD.startAnimating()
        view.addSubview(loadingHUD)
        loadingHUD.center = view.center
        
        // 调用批量删除API
        APIManager.shared.shortVideoWorksOperate(type: 3, worksIds: ids) { [weak self] result in
            loadingHUD.removeFromSuperview()
            
            switch result {
            case .success(_):
                self?.handleBatchDeletionSuccess(ids: ids)
            case .failure(let error):
                self?.showToast("删除失败: \(error.localizedDescription)")
            }
        }
    }
    
    // 处理批量删除成功
    private func handleBatchDeletionSuccess(ids: [String]) {
        // 获取要删除的索引
        let indicesToDelete = contentItems.indices.filter { ids.contains(contentItems[$0].id) }
        
        // 从数据源中移除
        for index in indicesToDelete.sorted(by: >) {
            contentItems.remove(at: index)
        }
        
        // 刷新表格
        tableView.reloadData()
        showToast("已删除 \(ids.count) 项内容")
        
        // 重置批量管理状态
        batchManageButtonTapped()
    }
    
    // 更新选框状态
    private func updateCheckbox(_ checkbox: UIView, isSelected: Bool) {
        if isSelected {
            checkbox.backgroundColor = UIColor(hex: "#FB6602")
            checkbox.layer.borderColor = UIColor(hex: "#FB6602").cgColor
            
            // 添加勾选图标 - 恢复原来的系统图标
            if checkbox.viewWithTag(100) == nil {
                let checkmark = UIImageView(image: UIImage(systemName: "checkmark"))
                checkmark.tintColor = .white
                checkmark.tag = 100
                checkbox.addSubview(checkmark)
                checkmark.snp.makeConstraints { make in
                    make.center.equalToSuperview()
                    make.width.height.equalTo(14)
                }
            }
        } else {
            checkbox.backgroundColor = .white
            checkbox.layer.borderColor = UIColor(hex: "#CCCCCC").cgColor
            
            // 移除勾选图标
            checkbox.viewWithTag(100)?.removeFromSuperview()
        }
    }
    
    // MARK: - 内容操作方法
    
    private func editItem(at index: Int) {
        let item = contentItems[index]
        print("编辑内容: \(item.title), ID: \(item.id)")
        
        // TODO: 跳转到编辑页面
        // 这里需要根据实际情况跳转到相应的编辑页面
        showToast("编辑功能即将上线")
    }
    
    private func unpublishItem(at index: Int) {
        let item = contentItems[index]
        
        // 根据当前状态决定操作名称和类型
        let actionName: String
        let operationType: Int
        
        switch item.publishStatus {
        case .published:
            actionName = "下架"
            operationType = 1 // 下架
        case .unpublished:
            actionName = "上架"
            operationType = 2 // 上架
        case .reviewing:
            actionName = "撤回"
            operationType = 1 // 假设撤回也使用下架接口
        case .reviewFailed:
            // 如果是审核失败状态，直接跳转到详情查看页面
            showReviewFailedDetails(for: item)
            return
        }
        
        print("\(actionName)内容: \(item.title), ID: \(item.id)")
        
        // 显示确认对话框
        let unAlert = CommonAlertView(
            title: "确认\(actionName)",
            message: "确定要\(actionName)这条内容吗？",
            leftButtonTitle: "取消",
            rightButtonTitle: "确定"
        )
        unAlert.onLeftButtonTap = { [weak unAlert] in unAlert?.dismiss() }
        unAlert.onRightButtonTap = { [weak self, weak unAlert] in
            unAlert?.dismiss()
            self?.performUnpublish(at: index, operationType: operationType)
        }
        unAlert.show(in: self.view)
    }
    
    private func performUnpublish(at index: Int, operationType: Int) {
        let item = contentItems[index]
        
        // 显示加载提示
        let loadingHUD = UIActivityIndicatorView(style: .large)
        loadingHUD.color = UIColor(hex: "#FB6602")
        loadingHUD.startAnimating()
        view.addSubview(loadingHUD)
        loadingHUD.center = view.center
        
        // 调用上架/下架接口
        APIManager.shared.shortVideoWorksOperate(type: operationType, worksIds: [item.id]) { [weak self] result in
            loadingHUD.removeFromSuperview()
            
            switch result {
            case .success(_):
                // 更新状态
                self?.handleUnpublishSuccess(at: index, operationType: operationType)
            case .failure(let error):
                self?.showToast("操作失败: \(error.localizedDescription)")
            }
        }
    }
    
    private func handleUnpublishSuccess(at index: Int, operationType: Int) {
        guard index < contentItems.count else { return }
        
        // 根据操作类型更新状态
        switch operationType {
        case 1: // 下架
            contentItems[index].publishStatus = .unpublished
            showToast("内容已下架")
        case 2: // 上架
            contentItems[index].publishStatus = .published
            showToast("内容已上架")
        default:
            break
        }
        
        // 刷新表格
        tableView.reloadRows(at: [IndexPath(row: index, section: 0)], with: .fade)
    }
    
    private func deleteItem(at index: Int) {
        let item = contentItems[index]
        print("删除内容: \(item.title), ID: \(item.id)")
        
        // 使用自定义弹窗CommonAlertView替换系统UIAlertController
        let alertView = CommonAlertView(
            title: "确认删除",
            message: "确定后，将永久删除该作品",
            leftButtonTitle: "取消",
            rightButtonTitle: "删除"
        )
        alertView.onLeftButtonTap = { [weak alertView] in
            alertView?.dismiss()
        }
        alertView.onRightButtonTap = { [weak self, weak alertView] in
            alertView?.dismiss()
            self?.performDelete(at: index)
        }
        alertView.show(in: self.view)
    }
    
    private func performDelete(at index: Int) {
        let item = contentItems[index]
        
        // 显示加载提示
        let loadingHUD = UIActivityIndicatorView(style: .large)
        loadingHUD.color = UIColor(hex: "#FB6602")
        loadingHUD.startAnimating()
        view.addSubview(loadingHUD)
        loadingHUD.center = view.center
        
        // 调用删除接口
        APIManager.shared.shortVideoWorksOperate(type: 3, worksIds: [item.id]) { [weak self] result in
            loadingHUD.removeFromSuperview()
            
            switch result {
            case .success(_):
                self?.handleDeleteSuccess(at: index)
            case .failure(let error):
                self?.showToast("删除失败: \(error.localizedDescription)")
            }
        }
    }
    
    private func handleDeleteSuccess(at index: Int) {
        guard index < contentItems.count else { return }
        
        // 从数据源中移除
        contentItems.remove(at: index)
        
        // 从表格中移除行
        tableView.deleteRows(at: [IndexPath(row: index, section: 0)], with: .fade)
        
        showToast("内容已删除")
    }
    
    // 添加方法处理审核失败详情查看
    func showReviewFailedDetails(for item: ContentManagementItem) {
        // 自定义审核失败弹窗
        let alertBg = UIView()
        alertBg.backgroundColor = UIColor.black.withAlphaComponent(0.45)
        alertBg.frame = UIScreen.main.bounds
        alertBg.isUserInteractionEnabled = true
        if let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
            window.addSubview(alertBg)
        } else {
            UIApplication.shared.keyWindow?.addSubview(alertBg)
        }

        let alertView = UIView()
        alertView.backgroundColor = .white
        alertView.layer.cornerRadius = 16
        alertView.clipsToBounds = true
        alertBg.addSubview(alertView)
        alertView.translatesAutoresizingMaskIntoConstraints = false
        alertView.centerXAnchor.constraint(equalTo: alertBg.centerXAnchor).isActive = true
        alertView.centerYAnchor.constraint(equalTo: alertBg.centerYAnchor).isActive = true
        alertView.widthAnchor.constraint(equalToConstant: 260).isActive = true

        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "审核失败"
        titleLabel.font = UIFont.boldSystemFont(ofSize: 14)
        titleLabel.textColor = UIColor(hex: "#FF6666")
        titleLabel.textAlignment = .center
        alertView.addSubview(titleLabel)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.topAnchor.constraint(equalTo: alertView.topAnchor, constant: 10).isActive = true
        titleLabel.heightAnchor.constraint(equalToConstant: 20).isActive = true
        titleLabel.centerXAnchor.constraint(equalTo: alertView.centerXAnchor).isActive = true
        
        // 警告icon
        let icon = UIImageView()
        if let warnImg = UIImage(named: "icon_exclamationmark_red") {
            icon.image = warnImg
        } else if let sfImg = UIImage(systemName: "exclamationmark.circle.fill") {
            icon.image = sfImg
            icon.tintColor = UIColor(hex: "#FF6666")
        }
        alertView.addSubview(icon)
        icon.translatesAutoresizingMaskIntoConstraints = false
        icon.centerYAnchor.constraint(equalTo: titleLabel.centerYAnchor).isActive = true
        icon.trailingAnchor.constraint(equalTo: titleLabel.leadingAnchor).isActive = true
        icon.widthAnchor.constraint(equalToConstant: 16).isActive = true
        icon.heightAnchor.constraint(equalToConstant: 16).isActive = true

        // 原因内容
        let reasonLabel = UILabel()
        reasonLabel.text = item.refuseReason?.isEmpty == false ? item.refuseReason : "暂无失败原因"
        reasonLabel.font = UIFont.systemFont(ofSize: 16)
        reasonLabel.textColor = UIColor(hex: "#333333")
        reasonLabel.textAlignment = .center
        reasonLabel.numberOfLines = 0
        alertView.addSubview(reasonLabel)
        reasonLabel.translatesAutoresizingMaskIntoConstraints = false
        reasonLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 11).isActive = true
        reasonLabel.leftAnchor.constraint(equalTo: alertView.leftAnchor, constant: 17).isActive = true
        reasonLabel.rightAnchor.constraint(equalTo: alertView.rightAnchor, constant: -17).isActive = true

        // 按钮
        let okBtn = UIButton(type: .system)
        okBtn.setTitle("我知道了", for: .normal)
        okBtn.setTitleColor(.white, for: .normal)
        okBtn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 14)
        okBtn.backgroundColor = UIColor(hex: "#FF8F1F")
        okBtn.layer.cornerRadius = 17.5
        okBtn.clipsToBounds = true
        alertView.addSubview(okBtn)
        okBtn.translatesAutoresizingMaskIntoConstraints = false
        okBtn.topAnchor.constraint(equalTo: reasonLabel.bottomAnchor, constant: 11).isActive = true
        okBtn.leftAnchor.constraint(equalTo: alertView.leftAnchor, constant: 32).isActive = true
        okBtn.rightAnchor.constraint(equalTo: alertView.rightAnchor, constant: -32).isActive = true
        okBtn.heightAnchor.constraint(equalToConstant: 35).isActive = true
        okBtn.bottomAnchor.constraint(equalTo: alertView.bottomAnchor, constant: -11).isActive = true
        okBtn.addTarget(self, action: #selector(dismissCustomAlert(_:)), for: .touchUpInside)
        // 让按钮能找到父视图
        okBtn.accessibilityHint = "customAlertBg"
        // 传递alertBg用于移除
        objc_setAssociatedObject(okBtn, UnsafeRawPointer(bitPattern: 0x1234)!, alertBg, .OBJC_ASSOCIATION_ASSIGN)
    }

    // 关闭自定义弹窗
    @objc private func dismissCustomAlert(_ sender: UIButton) {
        if let alertBg = objc_getAssociatedObject(sender, UnsafeRawPointer(bitPattern: 0x1234)!) as? UIView {
            alertBg.removeFromSuperview()
        }
    }
    
    // 设置发布按钮
    private func setupPublishButton() {
        // 创建发布按钮
        publishButton = UIButton(type: .custom)
        publishButton.setTitle("发布", for: .normal)
        publishButton.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        publishButton.setTitleColor(.white, for: .normal)
        publishButton.layer.cornerRadius = 23 // 高度46的一半
        publishButton.clipsToBounds = true
        publishButton.addTarget(self, action: #selector(publishButtonTapped), for: .touchUpInside)
        
        // 添加渐变背景
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor(hex: "#FF8D36").cgColor,
            UIColor(hex: "#FB6602").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        gradientLayer.frame = CGRect(x: 0, y: 0, width: 283, height: 46)
        gradientLayer.cornerRadius = 23
        
        // 将渐变层添加到按钮的layer
        publishButton.layer.insertSublayer(gradientLayer, at: 0)
        
        // 添加到视图
        view.addSubview(publishButton)
        
        // 设置按钮约束
        publishButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(283)
            make.height.equalTo(46)
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-12)
        }
        
        // 调整tableView的底部内容边距，确保内容不被发布按钮遮挡
        tableView.contentInset.bottom = 46 + 12 + 12 // 按钮高度 + 上边距 + 额外边距
    }
    
    // 发布按钮点击事件
    @objc private func publishButtonTapped() {
        print("发布按钮被点击")
        let vc = VideoRecordViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
}

// MARK: - UITextFieldDelegate
extension ContentManagementViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if let keyword = textField.text {
            // 更新搜索关键词并重新加载数据
            currentSearchKeyword = keyword
            loadNewData()
        }
        textField.resignFirstResponder()
        return true
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        // 键盘收起时检查搜索内容是否变化
        checkAndTriggerSearch()
    }
}

// MARK: - 表格视图代理和数据源
extension ContentManagementViewController: UITableViewDelegate, UITableViewDataSource {
    
    // 表格行数
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return contentItems.count
    }
    
    // 表格行高
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 219 + 8 + 16 // 将单元格高度更改为198点
    }
    
    // 配置单元格 - 修改为使用新的类名
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: "ContentManagementCell", for: indexPath) as? ContentManagementCell else {
            return UITableViewCell()
        }
        
        let item = contentItems[indexPath.row]
        
        // 注意这里：传递批量管理模式状态给单元格
        // 当 operationContainer 显示时，说明处于批量管理模式
        let isEditMode = !operationContainer.isHidden
        cell.configure(with: item, isEditMode: isEditMode)
        
        // 设置选中状态变化回调
        cell.onSelectionChanged = { [weak self, indexPath] isSelected in
            guard let self = self, indexPath.row < self.contentItems.count else { return }
            self.contentItems[indexPath.row].isSelected = isSelected
            self.updateSelectedItemsCount()
        }
        
        // 设置操作按钮点击回调
        cell.onEditTapped = { [weak self, indexPath] in
            guard let self = self, indexPath.row < self.contentItems.count else { return }
            self.editItem(at: indexPath.row)
        }
        
        cell.onUnpublishTapped = { [weak self, indexPath] in
            guard let self = self, indexPath.row < self.contentItems.count else { return }
            self.unpublishItem(at: indexPath.row)
        }
        
        cell.onDeleteTapped = { [weak self, indexPath] in
            guard let self = self, indexPath.row < self.contentItems.count else { return }
            self.deleteItem(at: indexPath.row)
        }
        
        return cell
    }
    
    // 更新选中项数量
    private func updateSelectedItemsCount() {
        let selectedCount = contentItems.filter { $0.isSelected }.count
        let allSelected = selectedCount == contentItems.count && contentItems.count > 0
        
        // 更新全选按钮状态
        if let checkbox = operationContainer.viewWithTag(101) {
            updateCheckbox(checkbox, isSelected: allSelected)
        }
        
        // 使用类变量 isSelectAll 替代 UserDefaults
        isSelectAll = allSelected
    }
}

// MARK: - 内容单元格
class ContentManagementCell: UITableViewCell {
    
    // 缩略图
    private let thumbnailView = UIImageView()
    
    // 选择按钮（用于批量操作）
    private let selectButton = UIButton(type: .custom)
    
    // 标题标签
    private let titleLabel = UILabel()
    
    // 标签容器
    private let tagsView = UIView()
    
    // 查看和点赞容器
    private let statsView = UIView()
    
    // 查看图标
    private let viewIcon = UIImageView()
    
    // 查看数
    private let viewCountLabel = UILabel()
    
    // 点赞图标
    private let likeIcon = UIImageView()
    
    // 点赞数
    private let likeCountLabel = UILabel()
    
    // 操作按钮容器
    private let actionsView = UIView()
    
    // 编辑按钮
    private let editButton = UIButton(type: .custom)
    
    // 下架按钮
    private let unpublishButton = UIButton(type: .custom)
    
    // 删除按钮
    private let deleteButton = UIButton(type: .custom)
    
    // 选中状态变化回调
    var onSelectionChanged: ((Bool) -> Void)?
    
    // 编辑按钮点击回调
    var onEditTapped: (() -> Void)?
    
    // 下架按钮点击回调
    var onUnpublishTapped: (() -> Void)?
    
    // 删除按钮点击回调
    var onDeleteTapped: (() -> Void)?
    
    // 是否选中
    private var isItemSelected: Bool = false {
        didSet {
            updateSelectButton()
            onSelectionChanged?(isItemSelected)
        }
    }
    
    // 新增：发布时间和审核状态标签
    private let publishTimeLabel = UILabel()
    private let statusLabel = UILabel()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 设置基本样式
        backgroundColor = UIColor(hex: "#F5F5F5")
        selectionStyle = .none
        
        // 创建卡片容器
        let cardView = UIView()
        cardView.backgroundColor = .white
        cardView.layer.cornerRadius = 8
        cardView.layer.shadowColor = UIColor.black.withAlphaComponent(0.05).cgColor
        cardView.layer.shadowOffset = CGSize(width: 0, height: 2)
        cardView.layer.shadowRadius = 4
        cardView.layer.shadowOpacity = 1
        contentView.addSubview(cardView)
        
        // 其余控件全部加到cardView
        cardView.addSubview(thumbnailView)
        cardView.addSubview(selectButton)
        cardView.addSubview(titleLabel)
        cardView.addSubview(tagsView)
        cardView.addSubview(statsView)
        cardView.addSubview(actionsView)
        cardView.addSubview(statusLabel)        // 只加到cardView
        cardView.addSubview(publishTimeLabel)   // 只加到cardView
        
        // 更新卡片尺寸以匹配198高度
        cardView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(8)
            make.bottom.equalTo(-8)
        }
        
        // 添加缩略图 - 修改为 100x166 尺寸
        thumbnailView.backgroundColor = UIColor(hex: "#EEEEEE")
        thumbnailView.contentMode = .scaleAspectFill
        thumbnailView.clipsToBounds = true
        thumbnailView.layer.cornerRadius = 4
        cardView.addSubview(thumbnailView)
        
        // 添加选择按钮 - 增大尺寸
        selectButton.backgroundColor = .white
        selectButton.layer.borderWidth = 1
        selectButton.layer.borderColor = UIColor(hex: "#CCCCCC").cgColor
        selectButton.layer.cornerRadius = 4 // 略微增大圆角
        selectButton.addTarget(self, action: #selector(selectButtonTapped), for: .touchUpInside)
        selectButton.isHidden = true // 默认隐藏
        cardView.addSubview(selectButton)
        
        // 添加标题标签 - 修改字体为16pt加粗
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold) // 改为16pt加粗
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.numberOfLines = 3
        cardView.addSubview(titleLabel)
        
        // 添加标签容器
        tagsView.backgroundColor = .clear
        cardView.addSubview(tagsView)
        
        // 添加统计容器
        statsView.backgroundColor = .clear
        cardView.addSubview(statsView)
        
        // 添加查看图标和数量
        viewIcon.image = UIImage(named: "views_icon")
        viewIcon.contentMode = .scaleAspectFit
        statsView.addSubview(viewIcon)
        
        viewCountLabel.font = UIFont.systemFont(ofSize: 12)
        viewCountLabel.textColor = UIColor(hex: "#999999")
        statsView.addSubview(viewCountLabel)
        
        // 添加点赞图标和数量
        likeIcon.image = UIImage(named: "likes_icon")
        likeIcon.contentMode = .scaleAspectFit
        statsView.addSubview(likeIcon)
        
        likeCountLabel.font = UIFont.systemFont(ofSize: 12)
        likeCountLabel.textColor = UIColor(hex: "#999999")
        statsView.addSubview(likeCountLabel)
        
        // 添加操作按钮容器
        actionsView.backgroundColor = .clear
        cardView.addSubview(actionsView)
        
        // 添加编辑按钮 - 统一样式与下架按钮一致
        editButton.setTitle("编辑", for: .normal)
        editButton.setTitleColor(UIColor(hex: "#777777"), for: .normal)  // 与下架按钮使用相同颜色
        editButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        editButton.backgroundColor = .white
        editButton.layer.cornerRadius = 4
        editButton.layer.borderWidth = 1
        editButton.layer.borderColor = UIColor(hex: "#777777").cgColor  // 与下架按钮使用相同边框色
        editButton.addTarget(self, action: #selector(editButtonTapped), for: .touchUpInside)
        actionsView.addSubview(editButton)
        
        // 添加下架按钮
        unpublishButton.setTitle("下架", for: .normal)
        unpublishButton.setTitleColor(UIColor(hex: "#777777"), for: .normal)
        unpublishButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        unpublishButton.backgroundColor = .white
        unpublishButton.layer.cornerRadius = 4
        unpublishButton.layer.borderWidth = 1
        unpublishButton.layer.borderColor = UIColor(hex: "#777777").cgColor
        unpublishButton.addTarget(self, action: #selector(unpublishButtonTapped), for: .touchUpInside)
        actionsView.addSubview(unpublishButton)
        
        // 添加删除按钮
        deleteButton.setTitle("删除", for: .normal)
        deleteButton.setTitleColor(UIColor(hex: "#FF3E4B"), for: .normal)
        deleteButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        deleteButton.backgroundColor = .white
        deleteButton.layer.cornerRadius = 4
        deleteButton.layer.borderWidth = 1
        deleteButton.layer.borderColor = UIColor(hex: "#FF3E4B").cgColor
        deleteButton.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
        actionsView.addSubview(deleteButton)
        
        // 设置约束
        setupConstraints(in: cardView)
        
        // 新增：状态标签
        statusLabel.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        statusLabel.textAlignment = .left
        statusLabel.clipsToBounds = true
        statusLabel.textColor = .white
        statusLabel.isHidden = true
        cardView.addSubview(statusLabel)
        // 新增：发布时间标签
        publishTimeLabel.font = UIFont.systemFont(ofSize: 12)
        publishTimeLabel.textColor = UIColor(hex: "#AAAAAA")
        publishTimeLabel.textAlignment = .left
        cardView.addSubview(publishTimeLabel)
    }
    
    private func setupConstraints(in cardView: UIView) {
        // 缩略图约束 - 修改为 100x166 尺寸
        thumbnailView.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.top.equalTo(15)
            make.width.equalTo(96)
            make.height.equalTo(160) // 更改为166高度
        }
        
        // 选择按钮约束 - 增大尺寸，移到左侧，垂直居中
        selectButton.snp.makeConstraints { make in
            make.left.equalTo(8) // 靠近左边缘
            make.centerY.equalToSuperview() // 垂直居中
            make.width.height.equalTo(26) // 从22增大到26
        }
        
        // 标题标签约束
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(thumbnailView.snp.right).offset(12)
            make.right.equalTo(-12)
            make.top.equalTo(thumbnailView)
        }
        
        // 标签容器约束
        tagsView.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.right.equalTo(-16)
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.height.equalTo(22)
        }
        
        // 统计容器约束
        statsView.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(tagsView.snp.bottom).offset(6)
            make.height.equalTo(20)
            make.width.equalTo(120)
        }
        
        // 查看图标约束
        viewIcon.snp.makeConstraints { make in
            make.left.equalTo(0)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
        // 查看数量约束
        viewCountLabel.snp.makeConstraints { make in
            make.left.equalTo(viewIcon.snp.right).offset(4)
            make.centerY.equalTo(viewIcon)
        }
        
        // 点赞图标约束
        likeIcon.snp.makeConstraints { make in
            make.left.equalTo(viewCountLabel.snp.right).offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
        // 点赞数量约束
        likeCountLabel.snp.makeConstraints { make in
            make.left.equalTo(likeIcon.snp.right).offset(4)
            make.centerY.equalTo(likeIcon)
        }
        
        // 操作按钮容器约束 - 固定在底部
        actionsView.snp.makeConstraints { make in
            make.right.equalTo(-8)
            make.bottom.equalTo(-11)
            make.height.equalTo(32)
            make.width.equalTo(180)
        }
        
        // 删除按钮约束
        deleteButton.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(56)
        }
        
        // 下架按钮约束
        unpublishButton.snp.makeConstraints { make in
            make.right.equalTo(deleteButton.snp.left).offset(-8)
            make.top.bottom.equalToSuperview()
            make.width.equalTo(56)
        }
        
        // 编辑按钮约束
        editButton.snp.makeConstraints { make in
            make.right.equalTo(unpublishButton.snp.left).offset(-8)
            make.top.bottom.equalToSuperview()
            make.width.equalTo(56)
        }
        
        // 新增：状态标签约束（放在标题左上角）
        statusLabel.snp.makeConstraints { make in
            make.left.equalTo(thumbnailView)
            make.top.equalTo(thumbnailView.snp.bottom).offset(11)
            make.height.equalTo(14)
        }
        // 新增：发布时间标签约束（放在底部左下角）
        publishTimeLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(likeIcon.snp.bottom).offset(8)
            make.height.equalTo(14)
        }
    }
    
    // 配置单元格
    func configure(with item: ContentManagementViewController.ContentManagementItem, isEditMode: Bool) {
        // 设置标题
        titleLabel.text = item.title
        
        // 加载缩略图
        thumbnailView.image = nil
        thumbnailView.backgroundColor = UIColor(hex: "#EEEEEE") // 默认背景色
        
        // 使用Kingfisher加载图片
        if !item.coverImageUrl.isEmpty, let imageUrl = URL(string: item.coverImageUrl) {
            thumbnailView.kf.setImage(
                with: imageUrl,
                placeholder: UIImage(named: "placeholder_image"),
                options: [.transition(.fade(0.2))],
                completionHandler: { result in
                    switch result {
                    case .success(_):
                        // 图片加载成功，移除灰色背景
                        self.thumbnailView.backgroundColor = .clear
                    case .failure(let error):
                        print("图片加载失败: \(error.localizedDescription)")
                        // 保持灰色背景
                    }
                }
            )
        }
        
        // 确保有标签数据后再设置标签
        setupTags(item.tags)
        
        // 设置查看数和点赞数
        viewCountLabel.text = "\(item.viewCount)"
        likeCountLabel.text = "\(item.likeCount)"
        
        // 设置选择框的显示状态 - 根据编辑模式决定
        selectButton.isHidden = !isEditMode
        
        // 设置布局位置
        if isEditMode {
            // 批量管理模式下的布局
            selectButton.snp.remakeConstraints { make in
                make.left.equalTo(16)
                make.centerY.equalToSuperview()
                make.width.height.equalTo(26) // 保持和初始设置一致
            }
            
            thumbnailView.snp.remakeConstraints { make in
                make.left.equalTo(selectButton.snp.right).offset(16)
                make.top.equalTo(16)
                make.width.equalTo(100)
                make.height.equalTo(166)
            }
            
            titleLabel.snp.remakeConstraints { make in
                make.left.equalTo(thumbnailView.snp.right).offset(12)
                make.right.equalTo(-16)
                make.top.equalTo(thumbnailView)
            }
        } else {
            // 普通模式下的布局
            thumbnailView.snp.remakeConstraints { make in
                make.left.equalTo(16)
                make.top.equalTo(16)
                make.width.equalTo(100)
                make.height.equalTo(166)
            }
            
            titleLabel.snp.remakeConstraints { make in
                make.left.equalTo(thumbnailView.snp.right).offset(12)
                make.right.equalTo(-16)
                make.top.equalTo(thumbnailView)
            }
        }
        
        // 设置选中状态
        isItemSelected = item.isSelected
        
        // 根据发布状态设置下架按钮文字
        switch item.publishStatus {
        case .published:
            unpublishButton.setTitle("下架", for: .normal)
            unpublishButton.isHidden = false
        case .unpublished:
            unpublishButton.setTitle("上架", for: .normal)
            unpublishButton.isHidden = false
        case .reviewing:
            // 审核中：不支持撤回，只保留删除按钮
            unpublishButton.isHidden = true
        case .reviewFailed:
            unpublishButton.setTitle("查看", for: .normal)
            unpublishButton.isHidden = false
        }
        // 仅在审核失败状态下显示编辑按钮
        editButton.isHidden = (item.publishStatus != .reviewFailed)
        
        // 确保操作按钮始终显示
        actionsView.isHidden = false
        
        // 强制立即更新约束
        layoutIfNeeded()
        
        // 新增：设置发布时间
        publishTimeLabel.text = item.updateTime.isEmpty ? "" : "发布时间：\(item.updateTime)"
        // 新增：设置审核状态标签
        // 先全量重置，确保复用无残留
        statusLabel.isHidden = false
        statusLabel.backgroundColor = .white // 统一为白色背景
        statusLabel.textColor = .black
        statusLabel.isUserInteractionEnabled = false
        
        switch item.publishStatus {
        case .published:
            statusLabel.text = "已发布"
            statusLabel.backgroundColor = .white
            statusLabel.textColor = UIColor(hex: "#666666")
            statusLabel.isHidden = true
        case .unpublished:
            statusLabel.text = "已下架"
            statusLabel.backgroundColor = .white
            statusLabel.textColor = UIColor(hex: "#666666")
            statusLabel.isHidden = true
        case .reviewing:
            statusLabel.text = "审核中"
            statusLabel.backgroundColor = .white
            statusLabel.textColor = UIColor(hex: "#FF6236")
            statusLabel.isHidden = false
        case .reviewFailed:
            statusLabel.text = "审核失败：查看原因"
            statusLabel.backgroundColor = .white
            statusLabel.textColor = UIColor(hex: "#EA0000")
            statusLabel.isHidden = false
            statusLabel.isUserInteractionEnabled = true
            // 移除旧手势，避免重复
            if let gestures = statusLabel.gestureRecognizers {
                for g in gestures { statusLabel.removeGestureRecognizer(g) }
            }
            let tap = UITapGestureRecognizer(target: self, action: #selector(statusLabelTapped))
            statusLabel.addGestureRecognizer(tap)
        }
    }
    
    // 设置标签
    private func setupTags(_ tags: [String]) {
        // 先清除现有标签
        tagsView.subviews.forEach { $0.removeFromSuperview() }
        
        var xOffset: CGFloat = 0
        
        for (index, tag) in tags.enumerated() {
            let tagLabel = UILabel()
            tagLabel.text = "#\(tag)"
            tagLabel.font = UIFont.systemFont(ofSize: 12)
            tagLabel.textColor = UIColor(hex: "#FB6602")
            
            // 计算标签宽度
            let size = (tag as NSString).size(withAttributes: [.font: UIFont.systemFont(ofSize: 12)])
            let tagWidth = size.width + 10
            
            tagsView.addSubview(tagLabel)
            tagLabel.snp.makeConstraints { make in
                make.left.equalTo(xOffset)
                make.centerY.equalToSuperview()
                make.width.equalTo(tagWidth + 10) // 加上#号和间距
            }
            
            xOffset += tagWidth + 16
            
            // 如果超出显示范围，不再显示
            if xOffset > tagsView.frame.width - 20 && index < tags.count - 1 {
                break
            }
        }
    }
    
    // 更新选择按钮状态
    private func updateSelectButton() {
        selectButton.isSelected = isItemSelected
        if isItemSelected {
            selectButton.backgroundColor = UIColor(hex: "#FB6602")
            selectButton.layer.borderColor = UIColor(hex: "#FB6602").cgColor
            
            // 使用系统提供的勾选标记
            if selectButton.viewWithTag(100) == nil {
                let checkmark = UIImageView(image: UIImage(systemName: "checkmark"))
                checkmark.tintColor = .white
                checkmark.tag = 100
                selectButton.addSubview(checkmark)
                checkmark.snp.makeConstraints { make in
                    make.center.equalToSuperview()
                    make.width.height.equalTo(14)
                }
            }
        } else {
            selectButton.backgroundColor = .white
            selectButton.layer.borderColor = UIColor(hex: "#CCCCCC").cgColor
            
            // 移除勾选图标
            selectButton.viewWithTag(100)?.removeFromSuperview()
        }
    }
    
    // MARK: - 按钮点击事件
    
    @objc private func selectButtonTapped() {
        isItemSelected = !isItemSelected
    }
    
    @objc private func editButtonTapped() {
        onEditTapped?()
    }
    
    @objc private func unpublishButtonTapped() {
        onUnpublishTapped?()
    }
    
    @objc private func deleteButtonTapped() {
        onDeleteTapped?()
    }
    
    // 新增：审核失败状态标签点击事件
    @objc private func statusLabelTapped() {
        // 触发查看原因弹窗
        if let tableView = self.superview as? UITableView,
           let indexPath = tableView.indexPath(for: self),
           let vc = tableView.delegate as? ContentManagementViewController {
            let item = vc.contentItems[indexPath.row]
            vc.showReviewFailedDetails(for: item)
        }
    }
}

